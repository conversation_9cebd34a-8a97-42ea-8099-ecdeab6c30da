// ==UserScript==
// @name         GMGN网络请求监控工具
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  监控页面网络请求的响应数据
// @match        https://gmgn.ai/*
// @grant        GM_xmlhttpRequest
// @grant        unsafeWindow
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // 强制最小金额
    const MIN_AMOUNT_USD = 20;
    const MIN_PARAM_VALUE = MIN_AMOUNT_USD / 1000;
    let currentCheckedUrl = ""; // 记录当前已检查的URL

    // 检查并处理min参数
    function checkMinParam() {
        const currentUrl = new URL(window.location.href);
        // 如果URL相同，则跳过
        if (currentUrl.toString() === currentCheckedUrl) {
            return;
        }

        currentCheckedUrl = currentUrl.toString();

        const minParam = currentUrl.searchParams.get('min');
        const currentValue = parseFloat(minParam) || 0;

        console.log(`[URL检查] 最小金额: ${MIN_PARAM_VALUE}, 当前值: ${currentValue}`);

        if (currentValue < MIN_PARAM_VALUE) {
            currentUrl.searchParams.set('min', MIN_PARAM_VALUE.toString());
            console.log(`[URL跳转] 跳转添加min=${MIN_PARAM_VALUE}`);
            window.location.href = currentUrl.toString();
            return false; // 返回false表示需要跳转
        }

        console.log(`[URL检查] min参数满足要求，继续执行`);
        return true; // 返回true表示可以继续执行
    }

    // 立即检查，如果需要跳转就不执行后面的代码
    if (!checkMinParam()) {
        console.log('[URL检查] 跳转了，直接退出');
        return; // 跳转了，直接退出
    }

    setInterval(checkMinParam, 1000);

    let globalParams = {};
    let globalHeaders = {
        "accept": "application/json, text/plain, */*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9",
        "content-type": "application/json",
        "cookie": document.cookie,
        "origin": "https://gmgn.ai",
        "priority": "u=1, i",
        "referer": window.location.href,
        "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": navigator.userAgent
    };

    let walletInfoMap = new Map();
    let topHolders = new Map();
    let topTraders = new Map();

    document.addEventListener('DOMContentLoaded', () => {
        waitForObserverElement();

        fetchTopHolders();
        fetchTopTraders();
        setInterval(() => {
            // 如果页面隐藏，则不更新
            if (!document.hidden) {
                if (window.location.pathname.includes('/token/')) {
                    fetchTopHolders();
                    fetchTopTraders();
                }
            }
        }, 60000);
    });

    async function fetchTopHolders() {
        try {
            const pathParts = window.location.pathname.split('/');
            const network = pathParts[1];
            const contract = pathParts[3];

            const queryParams = getParams();
            const headers = globalHeaders;

            const fullUrl = `https://gmgn.ai/vas/api/v1/token_holders/${network}/${contract}?` +
                `device_id=${queryParams.device_id}` +
                `&client_id=${queryParams.client_id}` +
                `&from_app=${queryParams.from_app}` +
                `&app_ver=${queryParams.app_ver}` +
                `&tz_name=${queryParams.tz_name}` +
                `&tz_offset=${queryParams.tz_offset}` +
                `&app_lang=${queryParams.app_lang}` +
                `&fp_did=${queryParams.fp_did}` +
                `&os=${queryParams.os}` +
                `&limit=100&cost=20&orderby=amount_percentage&direction=desc`;

            // console.log('[fetchTopHolders] 发起代币持有者请求:', fullUrl);
            const data = await makeRequest('GET', fullUrl, null, headers);

            topHolders.clear()
            const result = data.data.list;
            for (const item of result) {
                const address = item.address;
                const rank = parseInt(item.wallet_tag_v2.replace('TOP', ''));
                const percentage = (item.amount_percentage * 100).toFixed(2);
                topHolders.set(address, {
                    rank: rank,
                    percentage: percentage
                });
            }

            console.log('[fetchTopHolders] 更新TopHolders:', topHolders);
        } catch (error) {
            console.error('[fetchTopHolders] 请求失败:', error.message);
            throw error;
        }
    }

    async function fetchTopTraders() {
        try {
            const pathParts = window.location.pathname.split('/');
            const network = pathParts[1];
            const contract = pathParts[3];

            const queryParams = getParams();
            const headers = globalHeaders;

            const fullUrl = `https://gmgn.ai/vas/api/v1/token_traders/${network}/${contract}?` +
                `device_id=${queryParams.device_id}` +
                `&client_id=${queryParams.client_id}` +
                `&from_app=${queryParams.from_app}` +
                `&app_ver=${queryParams.app_ver}` +
                `&tz_name=${queryParams.tz_name}` +
                `&tz_offset=${queryParams.tz_offset}` +
                `&app_lang=${queryParams.app_lang}` +
                `&fp_did=${queryParams.fp_did}` +
                `&os=${queryParams.os}` +
                `&limit=100&orderby=profit&direction=desc`;

            // console.log('[fetchTopTraders] 发起代币交易者请求:', contract);
            const data = await makeRequest('GET', fullUrl, null, headers);

            topTraders.clear()
            const result = data.data.list;
            for (const item of result) {
                const address = item.address;
                const rank = parseInt(item.wallet_tag_v2.replace('TOP', ''));
                const profit = item.profit;
                topTraders.set(address, {
                    rank: rank,
                    profit: profit
                });
            }

            console.log('[fetchTopTraders] 更新topTraders:', topTraders);
        } catch (error) {
            console.error('[fetchTopTraders] 请求失败:', error.message);
            throw error;
        }
    }

    // 监控目标元素 当发现目标元素时，代表页面已经加载完成，可以开始监控
    function waitForObserverElement() {
        const observer = new MutationObserver((mutations) => {
            for (const mutation of mutations) {
                if (mutation.type === 'childList') {
                    for (const node of mutation.addedNodes) {
                        if (node.nodeType !== Node.ELEMENT_NODE) continue;

                        // 检查新增节点是否匹配目标选择器
                        let targetElement = null;

                        if (node.matches('.chakra-tabs__tab-panels > div:nth-child(1)')) {
                            targetElement = node;
                        } else {
                            targetElement = node.querySelector('.chakra-tabs__tab-panels > div:nth-child(1)');
                        }

                        if (targetElement) {
                            console.log('找到监控的目标元素:', targetElement);
                            initializeTradeHistory(targetElement);
                            observer.disconnect(); // 找到后停止观察
                            return;
                        }
                    }
                }
            }
        });

        // 开始观察DOM变化
        observer.observe(document.documentElement, {
            childList: true,
            subtree: true
        });

        console.log('开始监控 .chakra-tabs__tab-panels > div:nth-child(1) 元素');
    }

    // 初始化交易记录监控
    function initializeTradeHistory(targetElement) {
        // 监控特定元素创建
        const globalTabObserver = new MutationObserver((mutations) => {
            // 如果标签页不活动，不进行监控
            if (document.hidden) {
                return;
            }

            // 遍历所有发生的变化
            for (const mutation of mutations) {
                if (mutation.type === 'childList') {
                    //console.log('[globalTabObserver] addedNodes:', mutation.addedNodes);
                    // 监控用户滚动后的交易
                    for (const node of mutation.addedNodes) {
                        // 1. 必须是元素节点
                        if (node.nodeType !== Node.ELEMENT_NODE) continue;

                        // 2. 监控交易列表 首次载入，切换tab，点击k线都会触发 直接获取全部交易
                        // .g-table-body 刷新或者点击k线后就会创建并且添加交易 需要监控点击k线后的交易
                        if (node.matches('.g-table-body')) {
                            console.log('[globalTabObserver] 发现新的.g-table-body:', node);
                            // 检测是不是触发时间过滤器 如果是 则获取所有交易
                            // const timeFliter = document.querySelector('.flex.items-center.gap-2px.text-text-400');
                            if (true) {
                                //console.log('[globalTabObserver] 触发时间过滤器 获取所有交易的钱包信息');

                                const rows = node.querySelectorAll('div[data-index]');
                                for (const row of rows) {
                                    onTradeCreated(row);
                                }
                            }

                            continue;
                        }

                        // 3. 感觉会重复触发 前面触发.g-table-body 仍然会触发单独的row
                        if (node.matches('div[data-index]')) {
                            onTradeCreated(node);
                        }
                    }

                    // 监控网站更新的交易 是通过修改元素来实现的
                    // 如果有新的交易 原来是把data-index从n变成n+1 并且把style的top+45px
                    // 暂时不使用
                } else if (mutation.type === 'attributes') {
                    const node = mutation.target;

                    //console.log('[globalTabObserver] 属性变化:', {
                    //    element: node,
                    //    tagName: node.tagName,
                    //    attribute: mutation.attributeName,
                    //    oldValue: mutation.oldValue,
                    //    newValue: node.getAttribute(mutation.attributeName)
                    //});

                    // 通过A标签的获得最近的交易单行
                    if (node.tagName !== 'A') {
                        continue;
                    }

                    // 通过A标签的href确定是否是交易记录
                    const href = node.getAttribute('href');
                    if (!href || !href.includes('/tx/')) {
                        continue;
                    }

                    // 找到最近的交易单行
                    const closestDiv = node.closest('div[data-index]');
                    if (!closestDiv) {
                        console.log('[onTradeCreated] 找不到closestDiv');
                        continue;
                    }

                    onTradeCreated(closestDiv);
                }
            }
        });

        globalTabObserver.observe(targetElement, {
            childList: true, // 观察子节点的添加或移除
            subtree: true, // 观察所有后代节点（包括子节点和孙节点等）
            //attributes: true, // 观察属性变化
            //attributeOldValue: true, // 记录属性变化前的旧值
        });

        console.log('[initializeTradeHistory] globalTabObserver创建成功');
    }

    function onTradeCreated(row) {
        //console.log('[onTradeCreated] 交易创建:', row);

        // 隐藏不重要的信息 比如交易类型 交易数量 用其他信息替换 
        hideTradeInfo(row);
    }

    // 隐藏不重要的信息 比如交易类型 交易数量 用其他信息替换 
    function hideTradeInfo(row) {
        const divs = row.querySelectorAll('.flex.items-center.whitespace-nowrap.border-b.border-divider');
        if (divs.length === 0) {
            console.log('[hideTradeInfo] 找不到交易');
            return;
        }

        // 隐藏不是买入卖出的交易信息 比如 加池子
        if (divs.length === 5) {
            row.style.display = 'none';
            return;
        }

        // 隐藏交易类型
        const typeDiv = divs[1].querySelector('div');
        typeDiv.style.display = 'none';

        // 隐藏交易数量
        const quantityDiv = divs[3].querySelector('div');
        quantityDiv.style.display = 'none';

        createTradeSpan(divs);
    }

    const skipTxhash = new Set();
    // 把自定义信息 添加到交易单行中
    async function createTradeSpan(divs) {
        try {
            // 获取钱包地址
            const addressElement = divs[5].querySelector('[href]');
            if (!addressElement) {
                console.log('[createTradeSpan] 找不到钱包地址', divs[5]);
                return;
            }
            const address = addressElement.getAttribute('href').split('/').pop();

            // 获取txhash
            const txhashElement = divs[6].querySelector('[href]');
            if (!txhashElement) {
                console.log('[createTradeSpan] 找不到txhash', divs[6]);
                return;
            }
            const txhash = txhashElement.getAttribute('href').split('/').pop();

            // 跳过短时间重复交易 首次载入的关系 非常短的时间
            if (skipTxhash.has(txhash)) {
                console.log('[createTradeSpan] 跳过重复交易:', txhash);
                return;
            }

            skipTxhash.add(txhash);
            setTimeout(() => {
                skipTxhash.delete(txhash);
            }, 100);

            // // 检查是否是top100地址
            // if (!topHolders.has(address) && !topTraders.has(address)) {
            //     console.log('[createTradeSpan] 地址不在top100中，跳过:', address);
            //     return;
            // }

            let timestampElement = divs[0].querySelector('.flex');
            if (!timestampElement) {
                document.querySelector("div[color]:nth-child(1) > div:nth-child(3)").click();
                timestampElement = divs[0].querySelector('.flex');
            }
            const timestamp = parseTimeToTimestamp(timestampElement.textContent);

            // 等待200ms 让交易信息完全加载
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 获取钱包信息（异步）
            const result = await fetchWalletInfo(address, timestamp);
            console.log('[createTradeSpan] timestamp:', timestamp, '获取钱包信息:', result);

            // 0 = 时间 1 = 类型 2 = 总额 USD/SOL 3 = 数量 4 = 市值/价格 5 = 钱包地址 6 = txhash
            // 创建持仓信息到数量中
            createCurrentHoldSpan(divs[3], result);

            // 创建排名信息到数量中
            createRankSpan(divs[1], address);
        } catch (error) {
            console.log('[createTradeSpan] 获取钱包信息失败:', error.message);
        }
    }

    function createCurrentHoldSpan(div, result) {
        const currentHoldSpan = document.createElement('div');
        currentHoldSpan.style.backgroundColor = '#ffcccb';
        currentHoldSpan.style.padding = '5px';
        currentHoldSpan.style.borderRadius = '3px';
        currentHoldSpan.style.marginLeft = '0px';
        currentHoldSpan.style.display = 'inline-block';
        currentHoldSpan.style.fontSize = '14px';
        currentHoldSpan.setAttribute('class', 'trade-span');

        // 判断是否清仓（持仓价值为0或接近0）
        const holdValue = Math.trunc(parseFloat(result.total_value || 0)); // 持仓价值
        const isCleared = holdValue < 10; // 认为持仓价值小于10的为已清仓
        const statusSpan = isCleared ? `<span style="color:red">清仓</span>` : `<span style="color:green">持仓:$${holdValue}</span>`;

        currentHoldSpan.innerHTML = `${statusSpan}`;

        div.appendChild(currentHoldSpan);

        //console.log('[createCurrentHoldSpan] 添加持仓信息:', currentHoldSpan);
    }

    function createRankSpan(div, address) {
        let rankSpan = document.createElement('div');
        rankSpan.style.backgroundColor = '#ffcccb';
        rankSpan.style.padding = '5px';
        rankSpan.style.borderRadius = '3px';
        rankSpan.style.marginLeft = '0px';
        rankSpan.style.display = 'inline-block';
        rankSpan.style.fontSize = '14px';
        rankSpan.setAttribute('class', 'trade-span');

        let statusSpan = '';
        if (topHolders.has(address)) {
            const rank = topHolders.get(address).rank;
            statusSpan = `<span style="color:green">TOP${rank}</span>`;
        } else {
            rankSpan = null;
            return;
        }

        rankSpan.innerHTML = `${statusSpan}`;
        console.log('[createRankSpan] 添加排名信息:', statusSpan);

        div.appendChild(rankSpan);
    }

    let pendingRequests = new Map(); // 存储正在进行的请求
    const MAX_CONCURRENT_REQUESTS = 20; // 最大并发请求数
    // 最大并发请求当前不正常
    async function fetchWalletInfo(address, timestamp) {
        const now = Date.now();
        const CACHE_DURATION = 300 * 1000; // 5分钟缓存时间（毫秒）

        // 检查缓存是否存在且未过期
        if (walletInfoMap.has(address)) {
            const cachedData = walletInfoMap.get(address);
            const isExpired = now - cachedData.timestamp > CACHE_DURATION;

            if (!isExpired) {
                // 缓存未过期，直接返回
                console.log('[fetchWalletInfo] 缓存命中:', address, '剩余时间:', Math.round((CACHE_DURATION - (now - cachedData.timestamp)) / 1000), '秒');
                return cachedData.data;
            } else {
                // 缓存过期，删除旧缓存并重新请求
                console.log('[fetchWalletInfo] 缓存过期，重新请求:', address);
            }
        }

        // 检查是否已经有相同地址的请求在进行中
        if (pendingRequests.has(address)) {
            console.log('[fetchWalletInfo] 请求已在进行中，等待结果:', address);
            return await pendingRequests.get(address);
        }

        // 如果并发请求数超过限制，取消最旧的请求
        if (pendingRequests.size >= MAX_CONCURRENT_REQUESTS) {
            const oldestAddress = pendingRequests.keys().next().value;
            console.log('[fetchWalletInfo] 达到最大并发数，取消旧请求:', oldestAddress);

            // 取消最旧的请求（这里我们简单地从Map中删除，实际的网络请求可能仍在进行）
            pendingRequests.delete(oldestAddress);
        }

        // 创建新的请求Promise
        const requestPromise = performWalletActivityRequest(address, timestamp);

        // 将请求添加到pending列表
        pendingRequests.set(address, requestPromise);

        try {
            const result = await requestPromise;

            // 请求完成，从pending列表中移除
            pendingRequests.delete(address);

            console.log('[fetchWalletInfo] 请求完成:', address, '剩余并发:', pendingRequests.size);
            return result;
        } catch (error) {
            // 请求失败，从pending列表中移除
            pendingRequests.delete(address);
            console.error('[fetchWalletInfo] 请求失败:', address, error.message);
            throw error;
        }
    }

    // 执行钱包活动记录请求
    async function performWalletActivityRequest(address, timestamp) {
        try {
            // pathname 格式: /{network}/token/{contract}
            const pathParts = window.location.pathname.split('/');
            const network = pathParts[1];
            const contract = pathParts[3];

            const searchParams = getParams();
            const headers = globalHeaders;

            const fullUrl = `https://gmgn.ai/defi/quotation/v1/wallet_token_activity/${network}?` +
                `device_id=${searchParams.device_id}` +
                `&client_id=${searchParams.client_id}` +
                `&from_app=${searchParams.from_app}` +
                `&app_ver=${searchParams.app_ver}` +
                `&tz_name=${searchParams.tz_name}` +
                `&tz_offset=${searchParams.tz_offset}` +
                `&app_lang=${searchParams.app_lang}` +
                `&fp_did=${searchParams.fp_did}` +
                `&os=${searchParams.os}` +
                `&wallet=${address}` +
                `&token=${contract}` +
                `&limit=50`;

            console.log('[performWalletActivityRequest] 发起请求:', address);
            const responseData = await makeRequest('GET', fullUrl, null, headers);

            // 处理响应数据
            const activities = responseData.data.activities;

            // 计算统计数据
            let buy_volume = 0;
            let sell_volume = 0;
            let buys = 0;
            let sells = 0;
            let total_profit = 0;
            let balance = 0;
            let total_value = 0;
            let has_transfer_in = false;
            let has_transfer_out = false;

            activities.forEach(activity => {
                if (activity.event_type === 'buy' && activity.cost_usd) {
                    buy_volume += activity.cost_usd;
                    buys++;
                } else if (activity.event_type === 'sell' && activity.cost_usd) {
                    sell_volume += activity.cost_usd;
                    sells++;
                } else if (activity.event_type === 'transferIn') {
                    has_transfer_in = true;
                } else if (activity.event_type === 'transferOut') {
                    has_transfer_out = true;
                }

                // 计算余额（最新的代币数量）
                if (activity.token_amount && activity.event_type === 'buy') {
                    balance = parseFloat(activity.token_amount);
                }
            });

            // 计算总利润
            total_profit = sell_volume - buy_volume;

            // 估算当前价值（使用最新价格）
            const latestActivity = activities.find(a => a.price_usd && a.price_usd > 0);
            if (latestActivity && balance > 0) {
                total_value = balance * latestActivity.price_usd;
            }

            const result = {
                address: address,
                buy_volume,
                sell_volume,
                total_profit,
                buys,
                sells,
                total_value,
                balance: balance.toString(),
                has_transfer_in,
                has_transfer_out,
                activities: activities // 保留原始活动数据
            };

            // 更新缓存
            walletInfoMap.set(address, {
                data: result,
                timestamp: timestamp
            });

            console.log('[performWalletActivityRequest] 缓存已更新:', address, '缓存大小:', walletInfoMap.size);
            return result;
        } catch (error) {
            console.error('[performWalletActivityRequest] 请求失败或数据解析失败:', address, error.message);
            throw error;
        }
    }

    // 执行实际的钱包信息请求（备用方法）
    // 完整返回数据
    // {
    //     "code": 0,
    //     "reason": "",
    //     "message": "success",
    //     "data": {
    //         "balance": "70790586.83787",
    //         "total_value": 283.1139619853763,
    //         "buys": 8,
    //         "sells": 5,
    //         "total_profit": "230.108340150370363018682",
    //         "total_profit_pnl": "0.1706380400495641",
    //         "history_bought_cost": "1348.5172478725",
    //         "history_bought_amount": "321858727.6359",
    //         "history_sold_income": "1295.51162603969",
    //         "maker_token_tags": null,
    //         "is_open_or_close": true,
    //         "maker_info": {
    //             "address": "2M8R1HgqCtwX9My38hNvJkJWsFztu1K3xLSLf9jz94yP",
    //             "avatar": "",
    //             "ens": "",
    //             "tag": "",
    //             "tags": [],
    //             "tag_rank": {},
    //             "name": "",
    //             "nickname": "",
    //             "created_at": 1747208423,
    //             "twitter_username": "",
    //             "twitter_name": "",
    //             "is_blue_verified": false,
    //             "followers_count": 0,
    //             "twitter_description": "",
    //             "bind": false
    //         },
    //         "wallet_stat": {
    //             "last_hold_timestamp": 0,
    //             "win_rate_7d": "0.40625",
    //             "realized_profit_7d": "-1679.305368392409",
    //             "realized_pnl_7d": "-0.0995278350152748",
    //             "buy_7d": 126,
    //             "sell_7d": 92,
    //             "token_num": 33
    //         },
    //         "start_holding_at": 1749864984,
    //         "end_holding_at": null,
    //         "follow_count": 0,
    //         "remark_count": 1
    //     }
    // }
    async function performWalletRequest(address, timestamp) {
        try {
            // pathname 格式: /{network}/token/{contract}
            const pathParts = window.location.pathname.split('/');
            const network = pathParts[1];
            const contract = pathParts[3];

            const searchParams = getParams();
            const headers = globalHeaders;

            const fullUrl = `https://gmgn.ai/api/v1/wallet_token_info/${network}/${address}/${contract}?` +
                `device_id=${searchParams.device_id}` +
                `&client_id=${searchParams.client_id}` +
                `&from_app=${searchParams.from_app}` +
                `&app_ver=${searchParams.app_ver}` +
                `&tz_name=${searchParams.tz_name}` +
                `&tz_offset=${searchParams.tz_offset}` +
                `&app_lang=${searchParams.app_lang}` +
                `&fp_did=${searchParams.fp_did}` +
                `&os=${searchParams.os}` +
                `&limit=100` +
                `&maker=`;

            console.log('[performWalletRequest] 发起请求:', address);
            const responseData = await makeRequest('GET', fullUrl, null, headers);

            // 处理响应数据
            const data = responseData.data;
            const walletAddress = data.maker_info.address;
            const buy_volume = data.history_bought_cost;
            const sell_volume = data.history_sold_income;
            const total_profit = data.total_profit;
            const buys = data.buys;
            const sells = data.sells;
            const total_value = data.total_value;
            const balance = data.balance;

            const result = {
                address: walletAddress,
                buy_volume,
                sell_volume,
                total_profit,
                buys,
                sells,
                total_value,
                balance
            };

            // 更新缓存
            walletInfoMap.set(address, {
                data: result,
                timestamp: timestamp
            });

            console.log('[performWalletRequest] 缓存已更新:', address, '缓存大小:', walletInfoMap.size);
            return result;
        } catch (error) {
            console.error('[performWalletRequest] 请求失败或数据解析失败:', address, error.message);
            throw error;
        }
    }





















    


















    














    const originalOpen = XMLHttpRequest.prototype.open;
    const originalSend = XMLHttpRequest.prototype.send;

    XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
        this._method = method;
        this._url = url;
        //console.log(`[XHR] 准备请求: ${method} ${url}`);
        return originalOpen.apply(this, arguments);
    };

    XMLHttpRequest.prototype.send = function(data) {
        const xhr = this;

        // 监听响应
        xhr.addEventListener('readystatechange', function() {
            if (xhr.readyState === 4) {
                handleXHRResponse(xhr._method, xhr._url, xhr.status, xhr.responseText);
            }
        });

        return originalSend.apply(this, arguments);
    };

    // 自定义处理函数
    function handleXHRResponse(method, url, status, responseText) {
        // 在这里添加你的处理逻辑
        // 例如：过滤特定的 API 请求
        // if (url.includes('/api/')) {
        //     console.log(`[API请求] ${method} ${url} - 状态: ${status}`);

        //     try {
        //         const responseData = JSON.parse(responseText);
        //         console.log(`[API响应]`, responseData);
        //     } catch (e) {
        //         console.log(`[API响应] 非JSON格式:`, responseText);
        //     }
        // }

        if (url.includes('/account/iploc')) {
            processParams(url);
        }
    }

    let lastParams = {};

    function processParams(url) {
        console.group('processParams');
        try {
            const fullUrl = new URL(url, unsafeWindow.location.href).toString();
            const urlObj = new URL(fullUrl);
            const params = new URLSearchParams(urlObj.search);

            if (Object.keys(globalParams).length !== 0 && JSON.stringify(params) === JSON.stringify(lastParams)) {
                console.log('跳过相同请求参数');
                return;
            }

            // 正确地将URLSearchParams转换为普通对象
            for (const [key, value] of params.entries()) {
                globalParams[key] = value;
            }
            console.log('获取到请求参数:', globalParams);

            lastParams = params;

        } catch (e) {
            console.error('Failed to parse URL:', e);
        } finally { // 确保 groupEnd 总是被调用
            console.groupEnd();
        }
    }

    function getParams() {
        return globalParams;
    }

    /**
     * 发送 HTTP 请求，使用 GM_xmlhttpRequest，返回 Promise
     *
     * @param {string} method - HTTP 请求方法 (例如: "GET", "POST", "PUT", "DELETE")
     * @param {string} url - 完整的请求 URL
     * @param {object} [payload] - 可选的请求体数据对象 (用于 POST/PUT 请求)
     * @param {object} [headers] - 可选的请求头对象
     * @returns {Promise} - 返回 Promise，resolve 时传递解析后的响应数据，reject 时传递错误信息
     */
    function makeRequest(method, url, payload = null, headers = {}) {
        return new Promise((resolve, reject) => {
            // 构建 GM_xmlhttpRequest 的配置对象
            const requestConfig = {
                method: method,
                url: url,
                headers: headers,
                onload: function(response) {
                    if (response.status >= 200 && response.status < 300) {
                        try {
                            const responseData = JSON.parse(response.responseText);

                            // 检查业务逻辑成功（根据 GMGN API 的响应格式）
                            if (responseData && responseData.code === 0 && responseData.message === "success") {
                                resolve(responseData);
                            } else {
                                // 业务逻辑失败
                                const errorMessage = responseData ?
                                    `业务失败: Code ${responseData.code}, Message: ${responseData.message || ''} Reason: ${responseData.reason || ''}` :
                                    '响应格式不符合预期';
                                reject(new Error(errorMessage));
                            }
                        } catch (e) {
                            // JSON 解析失败
                            reject(new Error(`JSON 解析失败: ${e.message}`));
                        }
                    } else {
                        // HTTP 状态码错误
                        reject(new Error(`HTTP 请求失败，状态码: ${response.status}`));
                    }
                },
                onerror: function(response) {
                    reject(new Error(`网络错误: ${response.error || '未知错误'}`));
                },
                ontimeout: function() {
                    reject(new Error("请求超时"));
                }
            };

            // 如果有 payload，添加到请求配置中
            if (payload) {
                requestConfig.data = JSON.stringify(payload);
                // 为 POST/PUT 请求添加 Content-Type
                if (method.toUpperCase() === 'POST' || method.toUpperCase() === 'PUT') {
                    requestConfig.headers['Content-Type'] = 'application/json';
                }
            }

            // 发送请求
            try {
                GM_xmlhttpRequest(requestConfig);
            } catch (e) {
                reject(new Error(`发送请求时发生错误: ${e.message}`));
            }
        });
    }

    // 将 "06/19 09:00:33" 格式转换为timestamp
    function parseTimeToTimestamp(timeStr) {
        // 假设是当前年份
        const currentYear = new Date().getFullYear();

        // 解析时间字符串 "06/19 09:00:33"
        const [datePart, timePart] = timeStr.split(' ');
        const [month, day] = datePart.split('/');
        const [hour, minute, second] = timePart.split(':');

        // 创建Date对象
        const date = new Date(currentYear, parseInt(month) - 1, parseInt(day),
            parseInt(hour), parseInt(minute), parseInt(second));

        // 返回timestamp（毫秒）
        return date.getTime();
    }
})();