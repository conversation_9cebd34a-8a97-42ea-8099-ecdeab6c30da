// ==UserScript==
// @name         DOM元素监控器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  监控元素创建和属性修改，在右侧显示监控窗口
// <AUTHOR>
// @match        *://*/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 创建监控窗口
    function createMonitorWindow() {
        const monitorWindow = document.createElement('div');
        monitorWindow.id = 'dom-monitor-window';
        monitorWindow.innerHTML = `
            <div id="monitor-header">
                <h3>DOM监控器</h3>
                <div id="monitor-controls">
                    <button id="clear-log">清空</button>
                    <button id="toggle-monitor">暂停</button>
                    <button id="minimize-window">最小化</button>
                </div>
            </div>
            <div id="monitor-content">
                <div id="monitor-log"></div>
            </div>
        `;

        // 样式设置
        const style = document.createElement('style');
        style.textContent = `
            #dom-monitor-window {
                position: fixed;
                top: 50px;
                right: 20px;
                width: 400px;
                height: 600px;
                background: #ffffff;
                border: 2px solid #333;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                z-index: 999999;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                overflow: hidden;
                resize: both;
            }

            #monitor-header {
                background: #333;
                color: white;
                padding: 10px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: move;
            }

            #monitor-header h3 {
                margin: 0;
                font-size: 14px;
            }

            #monitor-controls button {
                margin-left: 5px;
                padding: 3px 8px;
                background: #555;
                color: white;
                border: none;
                border-radius: 3px;
                cursor: pointer;
                font-size: 10px;
            }

            #monitor-controls button:hover {
                background: #777;
            }

            #monitor-content {
                height: calc(100% - 50px);
                overflow-y: auto;
                padding: 10px;
                background: #f9f9f9;
            }

            #monitor-log {
                font-size: 11px;
                line-height: 1.4;
            }

            .log-entry {
                margin-bottom: 8px;
                padding: 5px;
                border-left: 3px solid #ccc;
                background: white;
                border-radius: 3px;
            }

            .log-entry.created {
                border-left-color: #28a745;
            }

            .log-entry.modified {
                border-left-color: #ffc107;
            }

            .log-entry.removed {
                border-left-color: #dc3545;
            }

            .log-timestamp {
                color: #666;
                font-size: 10px;
            }

            .log-element {
                font-weight: bold;
                color: #007bff;
            }

            .log-details {
                margin-top: 3px;
                color: #555;
                font-size: 10px;
            }

            .minimized {
                height: 50px !important;
            }

            .minimized #monitor-content {
                display: none;
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(monitorWindow);

        return monitorWindow;
    }

    // 添加日志条目
    function addLogEntry(type, element, details = '') {
        const logContainer = document.getElementById('monitor-log');
        if (!logContainer) return;

        const entry = document.createElement('div');
        entry.className = `log-entry ${type}`;

        const timestamp = new Date().toLocaleTimeString();
        const tagName = element.tagName ? element.tagName.toLowerCase() : 'text';
        const elementId = element.id ? `#${element.id}` : '';
        const elementClass = element.className ? `.${element.className.split(' ').join('.')}` : '';

        entry.innerHTML = `
            <div class="log-timestamp">${timestamp}</div>
            <div class="log-element">${tagName}${elementId}${elementClass}</div>
            <div class="log-details">${details}</div>
        `;

        logContainer.insertBefore(entry, logContainer.firstChild);

        // 限制日志条目数量
        const entries = logContainer.querySelectorAll('.log-entry');
        if (entries.length > 100) {
            entries[entries.length - 1].remove();
        }

        // 自动滚动到顶部
        logContainer.scrollTop = 0;
    }

    // 监控变量
    let isMonitoring = true;
    let observer = null;

    // 创建MutationObserver
    function createObserver() {
        observer = new MutationObserver(function(mutations) {
            if (!isMonitoring) return;

            mutations.forEach(function(mutation) {
                // 监控新增节点
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            addLogEntry('created', node, `新增元素`);
                        }
                    });

                    mutation.removedNodes.forEach(function(node) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            addLogEntry('removed', node, `移除元素`);
                        }
                    });
                }

                // 监控属性修改
                if (mutation.type === 'attributes') {
                    const oldValue = mutation.oldValue;
                    const newValue = mutation.target.getAttribute(mutation.attributeName);
                    addLogEntry('modified', mutation.target,
                        `属性 "${mutation.attributeName}" 从 "${oldValue}" 改为 "${newValue}"`);
                }
            });
        });

        // 开始观察
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeOldValue: true,
            attributeFilter: null // 监控所有属性
        });
    }

    // 初始化监控窗口
    function initMonitor() {
        const monitorWindow = createMonitorWindow();

        // 窗口拖拽功能
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };

        const header = document.getElementById('monitor-header');
        header.addEventListener('mousedown', function(e) {
            isDragging = true;
            dragOffset.x = e.clientX - monitorWindow.offsetLeft;
            dragOffset.y = e.clientY - monitorWindow.offsetTop;
        });

        document.addEventListener('mousemove', function(e) {
            if (isDragging) {
                monitorWindow.style.left = (e.clientX - dragOffset.x) + 'px';
                monitorWindow.style.top = (e.clientY - dragOffset.y) + 'px';
                monitorWindow.style.right = 'auto';
            }
        });

        document.addEventListener('mouseup', function() {
            isDragging = false;
        });

        // 控制按钮功能
        document.getElementById('clear-log').addEventListener('click', function() {
            document.getElementById('monitor-log').innerHTML = '';
        });

        document.getElementById('toggle-monitor').addEventListener('click', function() {
            isMonitoring = !isMonitoring;
            this.textContent = isMonitoring ? '暂停' : '开始';
            this.style.background = isMonitoring ? '#555' : '#28a745';
        });

        document.getElementById('minimize-window').addEventListener('click', function() {
            monitorWindow.classList.toggle('minimized');
            this.textContent = monitorWindow.classList.contains('minimized') ? '展开' : '最小化';
        });

        // 创建观察器
        createObserver();

        // 添加初始化日志
        addLogEntry('created', document.body, 'DOM监控器已启动');
    }

    // 等待页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initMonitor);
    } else {
        initMonitor();
    }

})();