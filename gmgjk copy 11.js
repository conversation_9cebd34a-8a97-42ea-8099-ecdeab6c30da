// ==UserScript==
// @name         GMGN网络请求监控工具
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  监控页面网络请求的响应数据
// @match        https://gmgn.ai/*
// @grant        GM_xmlhttpRequest
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';



    let globalNetwork = null;
    let globalContract = null;
    let searchParams = null;
    let traderDict = new Map();

    const getCostTime = (() => {
        const initTime = Date.now(); // 只初始化一次
        return () => Date.now() - initTime;
    })();

    // 检查当前标签页是否活动的函数
    function isTabActive() {
        return !document.hidden;
    }

    init();

    globalNetwork = window.location.pathname.split('/token/')[0].split('/')[1];
    let pathnameAfter = window.location.pathname.split('/token/')[1];
    globalContract = pathnameAfter.includes('_') ? pathnameAfter.split('_')[1] : pathnameAfter;

    // 倒计时
    let floatWindow = null;
    let mutationTimeout = null;
    let countdownInterval;
    let countdownSeconds = 1;

    function startCountdown() {
        function createFloatWindow(text = '') {
            const div = document.createElement('div');
            div.id = 'scroll-countdown-window';
            div.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                width: 150px;
                padding: 10px;
                background-color: #333;
                color: #fff;
                border-radius: 5px;
                box-shadow: 0 0 10px rgba(0,0,0,0.5);
                z-index: 9999;
                text-align: center;
                font-family: Arial, sans-serif;
            `;
            div.textContent = text;
            document.body.appendChild(div);
            return div;
        }

        if (!floatWindow) {
            floatWindow = createFloatWindow('等待滚动...');
        }

        clearTimeout(mutationTimeout);
        clearInterval(countdownInterval);

        countdownSeconds = 1;
        floatWindow.textContent = `倒计时: ${countdownSeconds}s`;

        // 倒计时显示
        countdownInterval = setInterval(() => {
            countdownSeconds--;
            floatWindow.textContent = `倒计时: ${countdownSeconds}s`;
            if (countdownSeconds <= 0) {
                clearInterval(countdownInterval);
            }
        }, 1000);

        // 5秒后检查
        mutationTimeout = setTimeout(() => {
            console.log('用户滚动结束，检查交易');
            setRowsAll();
            floatWindow.textContent = '检查完成';
        }, 1000);
    }

    function init() {
        initXHR();
        initEvent();
    }

    function initEvent() {
        addEventListener('DOMContentLoaded', () => {
            onDOMContentLoaded();
        });
    }

    let totalSupply = null;
    let marketCap = null;
    let g_tokenPrice = null;

    function initTokenInfo(refresh = false) {
        if (refresh) {
            totalSupply = null;
            marketCap = null;
            g_tokenPrice = null;
            console.log('[initTokenInfo] 刷新token信息');
        }

        if (!totalSupply) {
            if (!g_tokenPrice) {
                console.log('[initTokenInfo] 价格未初始化，不进行初始化 重试');
                setTimeout(() => {
                    initTokenInfo();
                }, 100);
                return;
            }

            const totalSupplyElement = document.querySelector('.p-12px.flex.flex-col.gap-10px.border-b-line-100 > div:nth-child(5) > div:nth-child(2)');
            if (!totalSupplyElement) {
                console.log('[initTokenInfo] 找不到totalSupplyElement 重试');
                setTimeout(() => {
                    initTokenInfo();
                }, 100);
                return;
            }

            const totalSupplyText = totalSupplyElement.textContent;
            if (totalSupplyText.includes('M')) {
                totalSupply = parseFloat(totalSupplyText.replace('M', '')) * 1000000;
                console.log('[initTokenInfo] 转换为数字 totalSupply:', totalSupply);
            }

            marketCap = totalSupply * g_tokenPrice;

            console.log('[initTokenInfo] 初始化token信息 totalSupply:', totalSupply, ' marketCap:', marketCap, ' g_tokenPrice:', g_tokenPrice);
        }
    }

    function onDOMContentLoaded() {
        // setTimeout(() => {
        //     getTopWalletStats('token_traders');
        // }, 700);

        // setTimeout(() => {
        //     getTopWalletStats('token_holders');
        // }, 1000);

        initTokenInfo();
        setupTradeHistoryObserver();
    }

    function setupTradeHistoryObserver() {
        const targetDiv = document.querySelector('.chakra-tabs__tab-panels > div:nth-child(1)');
        if (!targetDiv) {
            console.log('[setupTradeHistoryObserver] 找不到targetDiv，重试。');
            requestAnimationFrame(setupTradeHistoryObserver);
            return;
        }

        console.log('[setupTradeHistoryObserver] 找到了targetDiv。');

        //const isActivity = () => document.querySelector('div[role="tablist"] > button:nth-child(1)').textContent == '活动';
        const isAll = () => document.querySelector('#activity-filter > div > div:nth-child(1)').classList.contains('text-text-100') == true;

        // 监控特定元素创建
        const globalTabObserver = new MutationObserver((mutations) => {
            if (!isTabActive()) {
                console.log('[setupTradeHistoryObserver] 标签页不活动，不进行监控');
                return;
            }

            // 遍历所有发生的突变
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(node => {
                        //console.log('[setupTradeHistoryObserver] 新增节点:', node);
                        if (node.tagName === 'DIV') {
                            if (node.hasAttribute('data-index')) {
                                //console.log('[setupTradeHistoryObserver] 用户滚动:', node);

                                // 新的row 只需要隐藏 不需要恢复显示
                                if (!checkRowType(node)) {
                                    node.style.display = 'none';
                                }

                                // 滚动后更新全部的交易的倒计时
                                startCountdown();
                            }

                            // 主菜单是活动
                            // 子菜单是不是全部
                            if (node.classList.contains('g-table-body') && isAll()) {
                                //console.log('[setupTradeHistoryObserver] 新增节点:', node);
                                //console.log('[setupTradeHistoryObserver] 需要更新交易数据:', node);
                                setUsdFilter();
                                setRowsAll();
                            }
                        }
                    });

                } else if (mutation.type === 'attributes') {
                    // 处理属性变化
                    //console.log('[setupTradeHistoryObserver] 属性变化:', mutation.attributeName);
                    //console.log('[setupTradeHistoryObserver] 目标元素:', mutation.target);

                    const element = mutation.target;
                    if (element.tagName === 'A' && element.getAttribute('href').includes('/tx/')) {
                        const tradeDiv = element.closest('div[data-index]');
                        if (tradeDiv) {
                            removeRankSpan(tradeDiv);
                            if (!checkRowType(tradeDiv)) {
                                tradeDiv.style.display = 'none';
                            } else {
                                // 显示row
                                tradeDiv.style.display = '';
                            }

                            // console.log('[setupTradeHistoryObserver] 发现新的交易:', tradeDiv);
                            // 高亮显示交易行
                            setTradeAndHighLight(tradeDiv, 'yellow');
                        }
                    }
                }
            });
        });

        // 观察特定元素的创建
        globalTabObserver.observe(targetDiv, {
            attributes: true, // 观察属性变化
            childList: true, // 观察子节点的添加或移除
            subtree: true, // 观察所有后代节点（包括子节点和孙节点等）
            //characterData: true, // 观察文本内容变化
            //attributeOldValue: true, // 记录属性变化前的旧值
            //characterDataOldValue: true // 记录文本变化前的旧值
        });

        console.log('[setupTradeHistoryObserver] globalTabObserver创建成功');

        onObserverCreated();
    }

    // 初始化 现在切换到setupTradeHistoryObserver下监控g-table-body创建
    function onObserverCreated() {
        //setRowsAll();
    }

    function onTradeCreated() {

    }


    function getTradrRowAll() {
        const rows = document.querySelectorAll('.g-table-body [data-index]');
        return rows.length > 0 ? rows : 0;
    }

    // 更新全部交易
    function setRowsAll() {
        const checkScroll = document.querySelector('.g-table-wrapper.scrollbar');
        if (!checkScroll) {
            console.log('[setRowsAll] 找不到checkScroll 重试');
            setTimeout(setRowsAll, 200);
            return;
        }

        const targetDiv = getTradrRowAll();
        if (targetDiv.length === 0) {
            //console.log('[setRowsAll] 找不到targetDiv 重试');
            setTimeout(setRowsAll, 200);
            return;
        }

        targetDiv.forEach(row => {
            //console.log('[setRowsAll] 发现初始的交易', row);
            setTradeAndHighLight(row, 'blue');
        });
    }

    function changeRowsStyle(row) {
        // 类型
        const typeDiv = row.querySelector(':scope > div > div > div:nth-child(2) > div');
        typeDiv.style.display = "none";
        // 数量
        const quantityDiv = row.querySelector(':scope > div > div > div:nth-child(4) > div');
        quantityDiv.style.display = "none";

        //const overflowDiv = row.querySelector('.css-1xz8urt');
        //overflowDiv.style.overflow = 'visible';
        //const overflowDiv2 = row.querySelector('.css-18n6119');
        //overflowDiv2.style.overflow = 'visible';
    }

    function setUsdFilter() {
        const filterButton = document.querySelector('.css-1mjhmtt button');
        if (!filterButton) {
            console.log('[setUsdFilter] 找不到filterButton');
            return;
        }

        if (filterButton.querySelector('svg').classList.contains('text-text-100')) {
            //console.log('[setUsdFilter] filterButton已经启用');
            return;
        }

        filterButton.click();

        let inputMin = null;
        let inputMax = null;

        const inputs = document.querySelectorAll('.css-y7wxkx input');
        if (inputs.length != 2) {
            console.log('[setUsdFilter] 找不到inputs');
            return;
        }

        inputMin = inputs[0];
        inputMax = inputs[1];

        const minUsd = 20 / 1000;
        const maxUsd = 0 / 1000;

        setTimeout(() => {
            Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value').set.call(inputMin, minUsd);
            inputMin.dispatchEvent(new Event('input', { bubbles: true }));

            if (maxUsd) {
                Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value').set.call(inputMax, maxUsd);
                inputMax.dispatchEvent(new Event('input', { bubbles: true }));
            }

            // 应用
            document.querySelector('.css-1elebcq div:nth-child(2)').click();
        }, 1);
    }

    function getTopWalletStats(module) {
        if (module === '') {
            console.log(`[getTopWalletStats(${module})] 找不到module`);
            return;
        }

        if (!searchParams) {
            console.log(`[getTopWalletStats(${module})] 找不到searchParams`);
            return;
        }

        if (!globalNetwork || !globalContract) {
            console.log(`[getTopWalletStats(${module})] 找不到globalNetwork或者globalContract`);
            return;
        }

        let endParams = null;
        if (module === 'token_holders') {
            endParams = '&limit=100&cost=20&orderby=amount_percentage&direction=desc';
        } else if (module === 'token_traders') {
            endParams = '&limit=100&orderby=realized_profit&direction=desc';
        }

        // API 请求配置
        const apiUrl = `https://gmgn.ai/vas/api/v1/${module}/${globalNetwork}/${globalContract}?` +
            `device_id=${searchParams.device_id}` +
            `&client_id=${searchParams.client_id}` +
            `&from_app=${searchParams.from_app}` +
            `&app_ver=${searchParams.app_ver}` +
            `&tz_name=${searchParams.tz_name}` +
            `&tz_offset=${searchParams.tz_offset}` +
            `&app_lang=${searchParams.app_lang}` +
            `&fp_did=${searchParams.fp_did}` +
            `&os=${searchParams.os}` + endParams;

        // 配置请求头，模拟网站行为
        const headers = {
            'User-Agent': navigator.userAgent,
            'Referer': window.location.href,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cookie': document.cookie,
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        };

        //console.log(`[getTopWalletStats(${module})] 准备请求Url: ${apiUrl}`);

        // 发送 API 请求
        GM_xmlhttpRequest({
            method: 'GET',
            url: apiUrl,
            headers: headers,
            onload: function(response) {
                if (response.status === 200) {
                    try {
                        let data = JSON.parse(response.responseText);
                        let result = data.data.list;
                        result.forEach(item => {
                            const address = item.address;
                            const buy_volume = item.buy_volume_cur;
                            const sell_volume = item.sell_volume_cur;
                            const total_profit = item.profit;
                            const buys = item.buy_tx_count_cur;
                            const sells = item.sell_tx_count_cur;
                            const rank = parseInt(item.wallet_tag_v2.replace('TOP', ''));
                            const currentHold = Math.trunc(item.usd_value);
                            const currentHoldPercent = (item.amount_percentage * 100).toFixed(2);
                            const last_active_timestamp = item.last_active_timestamp;

                            const DEV = false;
                            if (DEV) {
                                console.log('地址:', address);
                                console.log('买入金额:', buy_volume);
                                console.log('卖出金额:', sell_volume);
                                console.log('总利润:', total_profit);
                                console.log('买入次数:', buys);
                                console.log('卖出次数:', sells);
                                console.log('当前持有:', currentHold);
                                console.log('当前持有百分比:', currentHoldPercent);
                                console.log('最后活跃时间:', last_active_timestamp);//int
                            }

                            let holderRank = 0;
                            let traderRank = 0;

                            const currentData = traderDict.get(address);
                            if (currentData) {
                                holderRank = currentData.holderRank;
                                traderRank = currentData.traderRank;
                            }

                            if (module === 'token_holders') {
                                holderRank = rank;
                            } else if (module === 'token_traders') {
                                traderRank = rank;
                            }

                            traderDict.set(address, {
                                holderRank: holderRank,
                                traderRank: traderRank,
                                buy_volume: buy_volume,
                                sell_volume: sell_volume,
                                profit: total_profit,
                                buys: buys,
                                sells: sells,
                                currentHold: currentHold,
                                currentHoldPercent: currentHoldPercent,
                                last_active_timestamp: last_active_timestamp
                            });
                        });
                    } catch (e) {
                        console.error('解析 JSON 失败:', e);
                        console.log('数据解析失败，请检查控制台');
                    }
                } else {
                    console.error('请求失败:', response.status, response.responseText);
                    console.log('请求失败: ' + response.status);
                }
            },
            onerror: function(error) {
                console.error('请求出错:', error);
                console.log('请求出错，请检查控制台');
            }
        });
    }

    function checkRowType(row) {
        const type = row.querySelector(':scope > div > div > div:nth-child(2) > div').textContent.trim();
        if (type !== '买入' && type !== '卖出') {
            return false;
        }

        return true;
    }

    function showRowCheck(row, hide = false) {
        let shouldShow = true;
        // 忽略夹子 金额小于20
        const buyUsdAmount = parseFloat(row.querySelector('div > div > div:nth-child(3) > div').title.replace('$', '').replace(',', '').trim());
        if (buyUsdAmount < 20) {
            console.log('[setTradeRank] 金额小于20，跳过');
            shouldShow = false;
        }

        // 忽略夹子 交易次数大于150
        const tradeCountText = row.querySelector('.css-1y6lpld').textContent.trim();
        if (parseInt(tradeCountText > 150) || tradeCountText.includes('K')) {
            console.log('[setTradeRank] 交易次数大于150，跳过');
            shouldShow = false;
        }

        if (shouldShow) {
            return true;
        } else {
            if (hide) {
                row.style.display = 'none';
            }
            return false;
        }
    }

    //let rowsrows = document.querySelectorAll('.g-table-body [data-index]')[0];

    async function setTradeRank(row) {
        if (!checkRowType(row)) {
            return;
        }

        if (!showRowCheck(row, true)) {
            return;
        }

        // 修改row的布局
        changeRowsStyle(row);

        const address = row.querySelector('a a[href*="/address/"]').getAttribute('href').split('/address/')[1];
        let result = traderDict.get(address);

        let last_active_div = document.querySelector('.g-table-body [data-index]').querySelector('.css-1ddjito .css-k008qs');
        if (!last_active_div) {
            document.querySelector('.css-vgxhkg').click();
            last_active_div = document.querySelector('.g-table-body [data-index]').querySelector('.css-1ddjito .css-k008qs');
        }
        const last_active_text = last_active_div.textContent;
        function stringToTimestamp(str) {
            return Math.floor(new Date(`${new Date().getFullYear()}/${str}`.replace(/-/g, '/')).getTime() / 1000);   
        }
        const last_active_timestamp = stringToTimestamp(last_active_text);

        const lastTx = document.querySelector('.g-table-body [data-index]').querySelector('.css-fg98qe a');

        if (!result || last_active_timestamp  > result.last_active_timestamp) {
            try {
                result = await getWalletStats(address);
                //console.log('[setTradeRank]结果:', result);

                if (!result) {
                    console.log('[setTradeRank] 无法获得getWalletStats结果');
                    return;
                }
            } catch (error) {
                console.error('[setTradeRank] 获取地址:', address, '失败:', error);
                return;
            }
        }

        removeRankSpan(row);

        const infoSpan = createInfoSpan(row, result);
        if (!infoSpan) {
            console.log('[setTradeRank] 创建InfoSpan失败');
        }

        if (result.holderRank !== 0 || result.traderRank !== 0) {
            const rankSpan = createRankSpan(row, result);
            if (!rankSpan) {
                console.log('[setTradeRank] 创建RankSpan失败');
            }
        }

        const currentHoldSpan = createCurrentHoldSpan(row, result);
        if (!currentHoldSpan) {
            console.log('[setTradeRank] 创建CurrentHoldSpan失败');
        }
    }

    // 创建显示交易者信息的Span
    function createInfoSpan(row, result) {
        let rank = result.traderRank;
        let profitInt = parseInt(result.profit);
        let buyVolumeInt = parseInt(result.buy_volume);
        let sellVolumeInt = parseInt(result.sell_volume);

        let infoSpan = document.createElement('div');
        infoSpan.style.backgroundColor = '#f0f8ff';
        infoSpan.style.padding = '5px';
        infoSpan.style.borderRadius = '3px';
        //infoSpan.style.marginLeft = '10px';
        infoSpan.style.marginLeft = '0px';
        infoSpan.style.display = 'inline-block';
        //infoSpan.style.fontSize = '12px';
        infoSpan.style.fontSize = '14px';

        infoSpan.setAttribute('class', 'ranker-info');

        //infoSpan.style.position = 'absolute';
        //infoSpan.style.right = '200px';
        //infoSpan.innerHTML = `<span style="color:#ff6600">交易者排名${rank}</span><span style="color:blue;">利润${profitInt}</span><span style="color:green;">买入${buyVolumeInt}</span><span style="color:red;">卖出${sellVolumeInt}</span>`;
        const spanProfit = `<span style="color:blue;">利润${profitInt}</span>`;
        const spanBuy = `<span style="color:green;">买入${buyVolumeInt}</span>`;
        const spanSell = `<span style="color:red;">卖出${sellVolumeInt}</span>`;

        infoSpan.innerHTML = spanProfit + spanBuy + spanSell;

        //style="position: relative;"
        //style="position: absolute;"
        // 交易者
        let position = row.querySelector('div > div > div:nth-child(6) a');
        //document.querySelector('.g-table-body [data-index] > div > div > div:nth-child(6) a');
        position.appendChild(infoSpan);

        return infoSpan;
    }

    function createRankSpan(row, result) {
        const rankSpan = document.createElement('div');
        rankSpan.style.backgroundColor = '#ffcccb';
        rankSpan.style.padding = '5px';
        rankSpan.style.borderRadius = '3px';
        //rankSpan.style.marginLeft = '10px';
        rankSpan.style.marginLeft = '0px';
        rankSpan.style.display = 'inline-block';
        //rankSpan.style.fontSize = '12px';
        rankSpan.style.fontSize = '14px';
        rankSpan.setAttribute('class', 'holder-rank');

        const holderRank = result.holderRank;
        const traderRank = result.traderRank;

        const spanHolder = holderRank ? `<span style="color:green">持有${holderRank}</span>` : '';
        const spanTrader = traderRank ? `<span style="color:green">交易${traderRank}</span>` : '';

        rankSpan.innerHTML = spanHolder + spanTrader;

        let position = row.querySelector('.css-r4nf31');
        position.appendChild(rankSpan);

        return rankSpan;
    }

    function createCurrentHoldSpan(row, result) {
        const currentHoldSpan = document.createElement('div');
        currentHoldSpan.style.backgroundColor = '#ffcccb';
        currentHoldSpan.style.padding = '5px';
        currentHoldSpan.style.borderRadius = '3px';
        //currentHoldSpan.style.marginLeft = '10px';
        currentHoldSpan.style.marginLeft = '0px';
        currentHoldSpan.style.display = 'inline-block';
        //currentHoldSpan.style.fontSize = '12px';
        currentHoldSpan.style.fontSize = '14px';
        currentHoldSpan.setAttribute('class', 'current-hold');

        const profit = parseFloat(result.profit).toFixed(0);

        const profitSpan = profit > 0 ? `<span style="color:green">+$${profit}</span>` : `<span style="color:red">$${profit}</span>`;

        if (result.currentHold > 0) {
            currentHoldSpan.innerHTML = `<span style="color:green">持仓$${result.currentHold}(${result.currentHoldPercent}%)</span>`;
        } else {
            currentHoldSpan.innerHTML = `<span style="color:red">清仓</span> ${profitSpan}`;
        }

        let position = row.querySelector('div > div > div:nth-child(4)');
        position.appendChild(currentHoldSpan);

        return currentHoldSpan;;
    }

    function removeRankSpan(row) {
        const rankerInfo = row.querySelectorAll('.ranker-info');
        //console.log('[removeRankSpan] rankerInfo.length: ', rankerInfo.length);
        if (rankerInfo.length > 0) {
            rankerInfo.forEach(item => {
                item.remove();
            });
        }

        const holderRankSpan = row.querySelectorAll('.holder-rank');
        if (holderRankSpan.length > 0) {
            holderRankSpan.forEach(item => {
                item.remove();
            });
        }

        const currentHoldSpan = row.querySelectorAll('.current-hold');
        if (currentHoldSpan.length > 0) {
            currentHoldSpan.forEach(item => {
                item.remove();
            });
        }
    }

    function setTradeAndHighLight(row, color = '#FFFACD') {
        row.style.backgroundColor = color;
        setTimeout(() => {
            row.style.backgroundColor = '';
        }, 500);

        setTradeRank(row);
    }

    function initXHR() {
        const originalOpen = XMLHttpRequest.prototype.open;
        const originalSend = XMLHttpRequest.prototype.send;

        XMLHttpRequest.prototype.open = function(method, url) {
            this._method = method;
            this._url = url;
            return originalOpen.apply(this, arguments);
        };

        XMLHttpRequest.prototype.send = function(body) {
            this.addEventListener('readystatechange', function() {
                if (this.readyState === 4 && this.status === 200) {
                    try {
                        const data = JSON.parse(this.responseText);
                        //console.log('[XHR] 请求地址:', this._url);
                        //console.log('[XHR] 响应数据:', data);

                        // 在这里可以对响应数据进行处理
                        onGetResult(this._url, data);
                    } catch (err) {
                        //console.log('[XHR] 请求地址:', this._url);
                        //console.log('[XHR] 响应文本:', this.responseText);
                    }
                }
            });
            return originalSend.apply(this, arguments);
        };
    }

    function onGetResult(url, jsonData) {
        if (searchParams === null) {
            // 提取查询参数部分
            const queryString = url.split('?')[1]; // 获取 ? 后面的部分
            searchParams = Object.fromEntries(new URLSearchParams(queryString));
            const ParamsTime = new Date().toISOString();
            //console.log('[getResult] ParamsTime:', ParamsTime);
            return;
        }

        if (url.includes('/api/v1/mutil_window_token_link_rug_vote')) {
            const baseUrl = url.split('?')[0].split('rug_vote/')[1];
            const network = baseUrl.split('/')[0];
            const contract = baseUrl.split('/')[1];
            if (network === globalNetwork && contract === globalContract) {
                return;
            }

            globalNetwork = network;
            globalContract = contract;
            console.log('[getResult] 切换到网络:', globalNetwork, '合约:', globalContract);
            initTokenInfo(true);
            setTimeout(() => {
                setRowsAll();
            }, 500);
            return;
        }

        // 获取代币价格
        if (url.includes('/api/v1/mutil_window_token_info')) {
            g_tokenPrice = jsonData.data[0].price.price;
            return;
        }
    }

    class RequestQueue {
        constructor(maxSize = 20, interval = 1000) {
            this.queue = []; // 请求队列
            this.maxSize = maxSize; // 队列最大容量
            this.interval = interval; // 请求间隔
            this.timerId = null; // 定时器ID
            this.activeRequest = null; // 当前活跃请求的xhr对象
        }

        // 添加请求到队列
        addRequest(requestConfig) {
            return new Promise((resolve, reject) => {
                // 队列满时移除最前面的请求
                if (this.queue.length >= this.maxSize) {
                    const removed = this.queue.shift();
                    removed.reject('[队列溢出] 请求被取消');
                }

                // 创建带取消功能的请求对象
                const request = {
                    ...requestConfig,
                    resolve,
                    reject,
                    xhr: null
                };

                this.queue.push(request);

                // 若未启动处理则立即启动
                if (!this.timerId) this.processQueue();
            });
        }

        // 处理队列
        processQueue() {
            if (this.queue.length === 0) {
                this.timerId = null;
                return;
            }

            const request = this.queue.shift();
            this.timerId = setTimeout(() => this.processQueue(), this.interval);

            try {
                this.activeRequest = GM_xmlhttpRequest({
                    ...request,
                    onload: (response) => {
                        this.activeRequest = null;
                        const processedResult = request.onload(response);
                        request.resolve(processedResult);
                    },
                    onerror: (error) => {
                        this.activeRequest = null;
                        const processedResult = request.onerror(error);
                        request.reject(processedResult);
                    },
                    onabort: () => {
                        this.activeRequest = null;
                        request.reject('[请求取消] 主动中止');
                    }
                });

                request.xhr = this.activeRequest;
            } catch (error) {
                request.reject(`[请求构造错误] ${error}`);
            }
        }

        // 取消所有请求
        cancelAll() {
            // 清除队列
            while (this.queue.length > 0) {
                const request = this.queue.shift();
                request.reject('[队列清除] 请求被取消');
            }

            // 中止当前请求
            if (this.activeRequest) {
                this.activeRequest.abort();
                this.activeRequest = null;
            }

            // 重置定时器
            clearTimeout(this.timerId);
            this.timerId = null;
        }
    }

    // 初始化全局请求队列
    const apiQueue = new RequestQueue(25, 100);

    async function getWalletStats(address) {
        // 前置校验
        if (!searchParams) {
            console.log('[getWalletStats] 找不到searchParams');
            return null;
        }

        if (!globalNetwork || !globalContract) {
            console.log('[getWalletStats] 找不到globalNetwork或globalContract');
            return null;
        }

        // 构造请求参数
        const apiUrl = `https://gmgn.ai/api/v1/wallet_token_info/${globalNetwork}/${address}/${globalContract}?` +
            `device_id=${searchParams.device_id}` +
            `&client_id=${searchParams.client_id}` +
            `&from_app=${searchParams.from_app}` +
            `&app_ver=${searchParams.app_ver}` +
            `&tz_name=${searchParams.tz_name}` +
            `&tz_offset=${searchParams.tz_offset}` +
            `&app_lang=${searchParams.app_lang}` +
            `&fp_did=${searchParams.fp_did}` +
            `&os=${searchParams.os}` +
            `&limit=100` +
            `&maker=`;

        const headers = {
            'User-Agent': navigator.userAgent,
            'Referer': window.location.href,
            'Accept': 'application/json, text/plain, */*',
            'Cookie': document.cookie,
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        };

        // 通过队列发送请求
        return apiQueue.addRequest({
            method: "GET",
            url: apiUrl,
            headers: headers,
            onload: handleResponse,
            onerror: handleError
        });

        function handleResponse(response) {
            try {
                const data = JSON.parse(response.responseText);

                const dataResult = data.data;
                const address = dataResult.maker_info.address;
                const buy_volume = dataResult.history_bought_cost;
                const sell_volume = dataResult.history_sold_income;
                const total_profit = dataResult.total_profit;
                const buys = dataResult.buys;
                const sells = dataResult.sells;
                const currentHold = Math.trunc(dataResult.total_value);
                const currentHoldPercent = ((parseFloat(dataResult.balance) / totalSupply) * 100).toFixed(2);

                // 如果没有数据 初始化
                const currentData = traderDict.get(address);
                if (!currentData) {
                    const result = {
                        holderRank: 0,
                        traderRank: 0,
                        buy_volume: buy_volume,
                        sell_volume: sell_volume,
                        profit: total_profit,
                        buys: buys,
                        sells: sells,
                        currentHold: currentHold,
                        currentHoldPercent: currentHoldPercent,
                    };
                    traderDict.set(address, result);
                    return result;
                }

                // 更新 holderRank和traderRank 保持不变
                const result = {
                    holderRank: currentData.holderRank,
                    traderRank: currentData.traderRank,
                    buy_volume: buy_volume,
                    sell_volume: sell_volume,
                    profit: total_profit,
                    buys: buys,
                    sells: sells,
                    currentHold: currentHold,
                    currentHoldPercent: currentHoldPercent,
                };

                traderDict.set(address, result);
                return result;
            } catch (e) {
                throw new Error(`数据解析失败: ${e.message}`);
            }
        }

        function handleError(error) {
            //throw new Error(`请求失败: ${error.status || error.message}`);
            return `请求失败: ${error.status || error.message}`;
        }
    }

    const DB_BASE_URL = 'http://localhost:4000';
    // --- 新增: 保存统计数据到后端的函数 ---
    async function db_saveTraderStats(statsData) {
        if (!globalNetwork ||!globalContract) {
            console.log('[db_saveTraderStats] 找不到globalNetwork或globalContract');
            return null;
        }

        const payload = {
            address: statsData.address,
            token_address: globalContract,
            chain: globalNetwork,
            holderRank: statsData.holderRank,
            traderRank: statsData.traderRank,
            buy_volume: statsData.buy_volume,
            sell_volume: statsData.sell_volume,
            profit: statsData.profit,
            buys: statsData.buys,
            sells: statsData.sells,
            currentHold: statsData.currentHold,
            currentHoldPercent: statsData.currentHoldPercent
        };

        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: `${DB_BASE_URL}/tradestats`,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                data: JSON.stringify(payload),
                onload: function(response) {
                    if (response.status === 200 || response.status === 201) { // 201 Created 也可能是成功的状态码
                        try {
                            const result = JSON.parse(response.responseText);
                            if (result.success) {
                                console.log('[saveTraderStats] 成功保存数据到后端:', result.message);
                                resolve(result.data);
                            } else {
                                console.error('[saveTraderStats] 后端保存数据失败:', result.error || result.message);
                                reject(new Error(result.error || result.message || '后端保存失败'));
                            }
                        } catch (e) {
                            console.error('[saveTraderStats] 解析后端保存响应失败:', e);
                            reject(new Error('解析后端保存响应失败'));
                        }
                    } else {
                        console.error('[saveTraderStats] 保存数据到后端请求失败:', response.status, response.statusText, response.responseText);
                        reject(new Error(`后端保存请求失败: ${response.status}`));
                    }
                },
                onerror: function(error) {
                    console.error('[Backend] 保存数据到后端网络错误:', error);
                    reject(new Error('后端保存网络错误'));
                }
            });
        });
    }
})();