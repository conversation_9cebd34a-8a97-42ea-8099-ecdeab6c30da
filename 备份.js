    let pendingRequests = new Map(); // 存储正在进行的请求
    const MAX_CONCURRENT_REQUESTS = 20; // 最大并发请求数
    // 最大并发请求当前不正常
    async function fetchWalletInfo(address, timestamp) {
        const now = Date.now();
        const CACHE_DURATION = 300 * 1000; // 1分钟缓存时间（毫秒）

        // 检查缓存是否存在且未过期
        if (walletInfoMap.has(address)) {
            const cachedData = walletInfoMap.get(address);
            const isExpired = now - cachedData.timestamp > CACHE_DURATION;

            if (!isExpired) {
                // 缓存未过期，直接返回
                console.log('[fetchWalletInfo] 缓存命中:', address, '剩余时间:', Math.round((CACHE_DURATION - (now - cachedData.timestamp)) / 1000), '秒');
                return cachedData.data;
            } else {
                // 缓存过期，删除旧缓存并重新请求
                console.log('[fetchWalletInfo] 缓存过期，重新请求:', address);
            }
        }

        // 检查是否已经有相同地址的请求在进行中
        if (pendingRequests.has(address)) {
            console.log('[fetchWalletInfo] 请求已在进行中，等待结果:', address);
            return await pendingRequests.get(address);
        }

        // 如果并发请求数超过限制，取消最旧的请求
        if (pendingRequests.size >= MAX_CONCURRENT_REQUESTS) {
            const oldestAddress = pendingRequests.keys().next().value;
            console.log('[fetchWalletInfo] 达到最大并发数，取消旧请求:', oldestAddress);

            // 取消最旧的请求（这里我们简单地从Map中删除，实际的网络请求可能仍在进行）
            pendingRequests.delete(oldestAddress);
        }

        // 创建新的请求Promise
        const requestPromise = performWalletRequest(address, timestamp);

        // 将请求添加到pending列表
        pendingRequests.set(address, requestPromise);

        try {
            const result = await requestPromise;

            // 请求完成，从pending列表中移除
            pendingRequests.delete(address);

            console.log('[fetchWalletInfo] 请求完成:', address, '剩余并发:', pendingRequests.size);
            return result;
        } catch (error) {
            // 请求失败，从pending列表中移除
            pendingRequests.delete(address);
            console.error('[fetchWalletInfo] 请求失败:', address, error.message);
            throw error;
        }
    }

    // 执行实际的钱包信息请求
    // 完整返回数据
    // {
    //     "code": 0,
    //     "reason": "",
    //     "message": "success",
    //     "data": {
    //         "balance": "70790586.83787",
    //         "total_value": 283.1139619853763,
    //         "buys": 8,
    //         "sells": 5,
    //         "total_profit": "230.108340150370363018682",
    //         "total_profit_pnl": "0.1706380400495641",
    //         "history_bought_cost": "1348.5172478725",
    //         "history_bought_amount": "321858727.6359",
    //         "history_sold_income": "1295.51162603969",
    //         "maker_token_tags": null,
    //         "is_open_or_close": true,
    //         "maker_info": {
    //             "address": "2M8R1HgqCtwX9My38hNvJkJWsFztu1K3xLSLf9jz94yP",
    //             "avatar": "",
    //             "ens": "",
    //             "tag": "",
    //             "tags": [],
    //             "tag_rank": {},
    //             "name": "",
    //             "nickname": "",
    //             "created_at": 1747208423,
    //             "twitter_username": "",
    //             "twitter_name": "",
    //             "is_blue_verified": false,
    //             "followers_count": 0,
    //             "twitter_description": "",
    //             "bind": false
    //         },
    //         "wallet_stat": {
    //             "last_hold_timestamp": 0,
    //             "win_rate_7d": "0.40625",
    //             "realized_profit_7d": "-1679.305368392409",
    //             "realized_pnl_7d": "-0.0995278350152748",
    //             "buy_7d": 126,
    //             "sell_7d": 92,
    //             "token_num": 33
    //         },
    //         "start_holding_at": 1749864984,
    //         "end_holding_at": null,
    //         "follow_count": 0,
    //         "remark_count": 1
    //     }
    // }
    async function performWalletRequest(address, timestamp) {
        try {
            // pathname 格式: /{network}/token/{contract}
            const pathParts = window.location.pathname.split('/');
            const network = pathParts[1];
            const contract = pathParts[3];

            const searchParams = getParams();
            const headers = globalHeaders;

            const fullUrl = `https://gmgn.ai/api/v1/wallet_token_info/${network}/${address}/${contract}?` +
                `device_id=${searchParams.device_id}` +
                `&client_id=${searchParams.client_id}` +
                `&from_app=${searchParams.from_app}` +
                `&app_ver=${searchParams.app_ver}` +
                `&tz_name=${searchParams.tz_name}` +
                `&tz_offset=${searchParams.tz_offset}` +
                `&app_lang=${searchParams.app_lang}` +
                `&fp_did=${searchParams.fp_did}` +
                `&os=${searchParams.os}` +
                `&limit=100` +
                `&maker=`;

            console.log('[performWalletRequest] 发起请求:', address);
            const responseData = await makeRequest('GET', fullUrl, null, headers);

            // 处理响应数据
            const data = responseData.data;
            const walletAddress = data.maker_info.address;
            const buy_volume = data.history_bought_cost;
            const sell_volume = data.history_sold_income;
            const total_profit = data.total_profit;
            const buys = data.buys;
            const sells = data.sells;
            const total_value = data.total_value;
            const balance = data.balance;

            const result = {
                address: walletAddress,
                buy_volume,
                sell_volume,
                total_profit,
                buys,
                sells,
                total_value,
                balance
            };

            // 更新缓存
            walletInfoMap.set(address, {
                data: result,
                timestamp: timestamp
            });

            console.log('[performWalletRequest] 缓存已更新:', address, '缓存大小:', walletInfoMap.size);
            return result;
        } catch (error) {
            console.error('[performWalletRequest] 请求失败或数据解析失败:', address, error.message);
            throw error;
        }
    }