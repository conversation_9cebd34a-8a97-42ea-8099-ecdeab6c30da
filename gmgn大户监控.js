// ==UserScript==
// @name         GMGN大户监控工具
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  监控页面网络请求的响应数据
// @match        https://gmgn.ai/*
// @grant        GM_xmlhttpRequest
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // 请求队列类
    class RequestQueue {
        constructor(maxSize = 20, interval = 1000) {
            this.queue = []; // 请求队列
            this.maxSize = maxSize; // 队列最大容量
            this.interval = interval; // 请求间隔
            this.timerId = null; // 定时器ID
            this.activeRequest = null; // 当前活跃请求的xhr对象
        }
    
        // 添加请求到队列
        addRequest(requestConfig) {
            return new Promise((resolve, reject) => {
                // 队列满时移除最前面的请求
                if (this.queue.length >= this.maxSize) {
                    const removed = this.queue.shift();
                    removed.reject('[队列溢出] 请求被取消');
                }
    
                // 创建带取消功能的请求对象
                const request = {
                    ...requestConfig,
                    resolve,
                    reject,
                    xhr: null
                };
    
                this.queue.push(request);
    
                // 若未启动处理则立即启动
                if (!this.timerId) this.processQueue();
            });
        }

        // 处理队列
        processQueue() {
            if (this.queue.length === 0) {
                this.timerId = null;
                return;
            }
    
            const request = this.queue.shift();
            this.timerId = setTimeout(() => this.processQueue(), this.interval);
    
            try {
                this.activeRequest = GM_xmlhttpRequest({
                    ...request,
                    onload: (response) => {
                        this.activeRequest = null;
                        const processedResult = request.onload(response);
                        request.resolve(processedResult);
                    },
                    onerror: (error) => {
                        this.activeRequest = null;
                        const processedResult = request.onerror(error);
                        request.reject(processedResult);
                    },
                    onabort: () => {
                        this.activeRequest = null;
                        request.reject('[请求取消] 主动中止');
                    }
                });
    
                request.xhr = this.activeRequest;
            } catch (error) {
                request.reject(`[请求构造错误] ${error}`);
            }
        }
    
        // 取消所有请求
        cancelAll() {
            // 清除队列
            while (this.queue.length > 0) {
                const request = this.queue.shift();
                request.reject('[队列清除] 请求被取消');
            }
    
            // 中止当前请求
            if (this.activeRequest) {
                this.activeRequest.abort();
                this.activeRequest = null;
            }
    
            // 重置定时器
            clearTimeout(this.timerId);
            this.timerId = null;
        }
    }
    // 初始化全局请求队列
    const apiQueue = new RequestQueue(20, 100);

    let firstTx = '';
    let g_searchParams = null;
    let oldToken = '';
    let currentCheckRunId  = 0;

    const getCurrentToken = () => { return window.location.pathname.split('?')[0].split('/')[3] };

    const traderDict = new Map();
    const g_tokenInfo = new Map();

    initXHR();

    document.addEventListener('DOMContentLoaded', () => {
        initAndStartMonitor();
    });

    async function initAndStartMonitor() {
        try {
            if (!isTabActive()) {
                console.log('[checkRows] 标签页不活动,不进行监控.');
                return;
            }

            const currentToken = getCurrentToken();
            if (oldToken !== currentToken) {
                oldToken = currentToken;
                console.log('[checkRows] 发现新的token,准备更新数据.');
                
                traderDict.clear();
                await Promise.all([
                    setUsdFilter(),
                    fetchTopWalletStats('token_traders'),
                    fetchTopWalletStats('token_holders')
                ]);
            }

            await checkRows();
        } catch (error) {
            console.error('初始化失败:', error);
        } finally {
            setTimeout(initAndStartMonitor, 500);
        }
    }

    async function checkRows() {
        //  如果tx不存在证明列表还未加载
        const txNowElement = document.querySelector('.g-table-body [data-index] a[href*="/tx/"]');
        if (!txNowElement) {
            console.log('[checkRows] 初始化未完成');
            return;
        }

        if (!isTabAll()) {
            console.log('[checkRows] 未切换到活动 -> 全部');
            return;
        }

        const txNow = txNowElement.href.split('/').pop();
        if (txNow === firstTx) {
            return;
        } else {
            firstTx = txNow;
            console.log('[checkRows] 准备更新交易');
        }

        const rows = document.querySelectorAll('.g-table-body .text-Highlight');
        if (rows.length === 0) {
            console.log('[checkRows] 未找到交易');
            return;
        }

        // 取消之前的网络请求队列
        apiQueue.cancelAll();

        // 生成新的运行 ID
        currentCheckRunId++;
        const runId = currentCheckRunId; // 获取当前轮次的 ID

        for (const row of rows) {
            // 检查此轮次是否已被新的轮次取代 (可选优化，可以在循环开始时检查)
            if (runId !== currentCheckRunId) {
                console.log('[checkRows] 检测到取消 循环被中断');
                break; // 如果 ID 不匹配，停止处理剩余行
            }

            setTradeSpan(row, runId);
        }
    }

    async function setTradeSpan(row, runId) {
        //setElementHighLight(row, 'red');

        if (hasInfoSpan(row)) {
            removeInfoSpan(row)
        }

        const address = row.querySelector('a a[href*="/address/"]').getAttribute('href').split('/address/')[1];
        let result = await getWalletStats(address);
        if (!result) {
            return;
        }

        // **检查点：DOM 操作前检查**
        if (runId !== currentCheckRunId) {
            console.log('[setTradeSpan] DOM 操作前检测到取消');
            return; // 如果 ID 不匹配，则不执行后续的 DOM 操作
        }

        createInfoSpan(row, result);
        return;
    }

    function createInfoSpan(row, result) {
        // 检查row是否被删除 await是异步
        if (!row.isConnected) {
            console.log('[setTradeRank] row已被删除，跳过处理');
            return;
        }

        let infoSpan = document.createElement('div');
        infoSpan.style.backgroundColor = '#f0f8ff';
        infoSpan.style.padding = '5px';
        infoSpan.style.borderRadius = '3px';
        infoSpan.style.marginLeft = '0px';
        infoSpan.style.display = 'inline-block';
        infoSpan.style.fontSize = '14px';
        infoSpan.setAttribute('class', 'info-span');

        const profit = formatNumberToKM(result.profit);
        const buyVolume = formatNumberToKM(result.buy_volume);
        const sellVolume = formatNumberToKM(result.sell_volume);

        const spanProfit = `<span style="color:blue;">利润${profit}</span>`;
        const spanBuy = `<span style="color:green;">买入${buyVolume}</span>`;
        const spanSell = `<span style="color:red;">卖出${sellVolume}</span>`;

        infoSpan.innerHTML = spanProfit + spanBuy + spanSell;

        const div = row.querySelector('div > div > div:nth-child(6)');
        if (!div) {
            console.error('[createInfoSpan] 无法找到目标父级 div');
            return null;
        }

        // 设置父容器 overflow: visible 防止 infoSpan 被裁剪
        div.style.overflow = 'visible';

        const position = div.querySelector('a');
        if (!position) {
            console.error('[createInfoSpan] 无法找到目标 a 标签');
            return null;
        }

        //position.appendChild(infoSpan);
        // 将 infoSpan 插入为 position 的第一个子元素
        position.insertBefore(infoSpan, position.firstChild);
        return infoSpan;
    }

    function removeInfoSpan(row) {
        const rankerInfo = row.querySelectorAll('.info-span');
        if (rankerInfo.length > 0) {
            for (const item of rankerInfo) {
                item.remove();
            }
        }

        const holderRankSpan = row.querySelectorAll('.holder-span');
        if (holderRankSpan.length > 0) {
            for (const item of holderRankSpan) {
                item.remove();
            }
        }

        const currentHoldSpan = row.querySelectorAll('.current-hold-span');
        if (currentHoldSpan.length > 0) {
            for (const item of currentHoldSpan) {
                item.remove();
            }
        }
    }

    function hasInfoSpan(row) {
        return row.querySelector('.info-span') !== null;
    }

    function setElementHighLight(element, color = '#FFFACD', duration = 5000) {
        element.style.backgroundColor = color;
        setTimeout(() => {
            if (!element.isConnected) {
                element.style.backgroundColor = '';
            }
        }, 5000);
    }

    async function setUsdFilter() {
        let marketCap = 0;
        try {
            const tokenInfo = await getTokenInfo();
            marketCap = tokenInfo.marketCap;
        } catch (e) {
            throw new Error('[setUsdFilter] 无法获取marketCap:', e);
        }

        const filterButton = document.querySelector('.css-1mjhmtt button');
        if (!filterButton) {
            throw new Error('[setUsdFilter] 找不到filterButton');
        }

        filterButton.click();

        const inputs = document.querySelectorAll('.css-y7wxkx input');
        if (inputs.length != 2) {
            throw new Error('[setUsdFilter] 找不到inputs');
        }

        let inputMin = inputs[0];
        let inputMax = inputs[1];
        
        let minUsd = marketCap / 2000;
        if (minUsd >  5000) {
            minUsd = 5000;
        }

        console.log('[setUsdFilter] marketCap:', marketCap , 'minUsd:', minUsd);
        let maxUsd = 0;

        setTimeout(() => {
            Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value').set.call(inputMin, minUsd / 1000);
            inputMin.dispatchEvent(new Event('input', { bubbles: true }));

            if (maxUsd) {
                Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value').set.call(inputMax, maxUsd / 1000);
                inputMax.dispatchEvent(new Event('input', { bubbles: true }));
            }

            // 应用
            document.querySelector('.css-1elebcq div:nth-child(2)').click();
        }, 1);
    }

    function getTokenInfo() {
        getTokenInfo.runCount = (getTokenInfo.runCount || 0) + 1;
        console.log('[getTokenInfo] 运行次数', getTokenInfo.runCount);

        return new Promise((resolve, reject) => {
            const network = window.location.pathname.split('/token/')[0].split('/')[1];
            const contract = window.location.pathname.split('/token/')[1];

            if (g_tokenInfo.has(contract)) {
                const info = g_tokenInfo.get(contract);
                console.log('[getTokenInfo] 从缓存中获取', contract, info);
                return resolve(info);    
            }

            const searchParams = getSearchParams();
            if (!searchParams) {
                return reject(`[fetchWalletStats] 找不到searchParams`);
            }

            const apiUrl = `https://gmgn.ai/api/v1/mutil_window_token_info?` +
                `device_id=${searchParams.device_id}` +
                `&client_id=${searchParams.client_id}` +
                `&from_app=${searchParams.from_app}` +
                `&app_ver=${searchParams.app_ver}` +
                `&tz_name=${searchParams.tz_name}` +
                `&tz_offset=${searchParams.tz_offset}` +
                `&app_lang=${searchParams.app_lang}` +
                `&fp_did=${searchParams.fp_did}` +
                `&os=${searchParams.os}`;
    ;

            const headers = {
                'User-Agent': navigator.userAgent,
                'Referer': window.location.href,
                'Accept': 'application/json, text/plain, */*',
                'Cookie': document.cookie,
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin'
            };

            const params = {
                "chain": network,
                "addresses":[contract]
            }

            GM_xmlhttpRequest({
                method: 'POST',
                url: apiUrl,
                headers: headers,
                data: JSON.stringify(params),
                onload: function(response) {
                    if (response.status === 200) {
                        try {
                            const data = JSON.parse(response.responseText);
                            if (data.message !== 'success' || data.data.length === 0) {
                                return reject(`[getTokenSupply] 接口返回错误: ${data}`);
                            }

                            const totalSupply = parseInt(data.data[0].total_supply);
                            const price = parseFloat(data.data[0].price.price);
                            const marketCap = totalSupply * price;

                            g_tokenInfo.set(contract, {
                                totalSupply: totalSupply,
                                price: price,
                                marketCap: marketCap
                            });

                            return resolve(g_tokenInfo.get(contract));
                        } catch (e) {
                            return reject(`解析JSON失败`);
                        }
                    } else {
                        return reject(`请求失败: ${response.status}`);
                    }
                },
                onerror: function(error) {
                    return reject(`请求失败: ${error}`);
                }
            });
        });
    }








    // 检查是不是活动 -> 全部
    function isTabAll() {
        const checkTab = document.querySelector('.chakra-tabs__tablist > div:nth-child(1)').classList.contains('text-text-100');
        const checkAll = document.querySelector('#activity-filter > div > div:nth-child(1)').classList.contains('text-text-100');
        return checkTab && checkAll;
    }

    // 检查当前标签页是否活动的函数
    function isTabActive() {
        return !document.hidden;
    }

    /**
     * 将大数字格式化为 K 或 M 的缩写形式
     * @param {number|string} num - 需要格式化的数字
     * @returns {string|number} - 格式化后的字符串或原始数字
     */
    function formatNumberToKM(num) {
        num = parseInt(num);
        if (isNaN(num)) return num;
        if (num >= 10000000) {
            return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M';
        }
        if (num >= 1000) {
            return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
        }
        return num;
    }

/**
 * 等待指定的元素出现在 DOM 中。
 * @param {string} selector - 用于查找元素的 CSS 选择器。
 * @param {Element} [container=document.body] - 在哪个容器元素内查找，默认为 document.body。
 * @param {number} [timeout=30000] - 等待的超时时间（毫秒），默认为 30 秒。
 * @returns {Promise<Element>} - 返回一个 Promise，解析为找到的元素，或者在超时后拒绝。
 */
async function waitForElement(selector, container = document.body, timeout = 30000) {
    return new Promise((resolve, reject) => {
        // 首先检查元素是否已存在
        const existingElement = container.querySelector(selector);
        if (existingElement) {
            resolve(existingElement);
            return;
        }

        // 设置超时计时器
        const timeoutId = setTimeout(() => {
            observer.disconnect(); // 超时后停止观察
            reject(new Error(`等待元素 "${selector}" 超时 (${timeout}ms)`));
        }, timeout);

        // 创建 MutationObserver 来观察 DOM 变化
        const observer = new MutationObserver((mutationsList, obs) => {
            // 检查新添加的节点或其子节点是否匹配选择器
            for (const mutation of mutationsList) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    const foundElement = container.querySelector(selector);
                    if (foundElement) {
                        clearTimeout(timeoutId); // 清除超时计时器
                        obs.disconnect(); // 找到元素后停止观察
                        resolve(foundElement); // 解析 Promise 并返回找到的元素
                        return;
                    }
                }
            }
        });

        // 配置观察器：观察子节点的添加和移除
        const config = { childList: true, subtree: true };

        // 开始观察目标容器
        observer.observe(container, config);
    });
}




















    
    async function getWalletStats(address) {
        if (traderDict.has(address)) {
            return traderDict.get(address);
        }

        try {
            return await fetchWalletStats(address);
        }
        catch (e) {
            return null;
        }
    }

    function fetchTopWalletStats(module) {
        return new Promise((resolve, reject) => {
            try {
                const searchParams = getSearchParams();
                if (!searchParams) {
                    return reject(new Error(`[fetchTopWalletStats] 找不到searchParams`));
                }

                let endParams = null;
                if (module === 'token_holders') {
                    endParams = '&limit=100&cost=20&orderby=amount_percentage&direction=desc';
                } else if (module === 'token_traders') {
                    endParams = '&limit=100&orderby=realized_profit&direction=desc';
                }

                const network = window.location.pathname.split('/token/')[0].split('/')[1];
                const contract = window.location.pathname.split('/token/')[1];

                const apiUrl = `https://gmgn.ai/vas/api/v1/${module}/${network}/${contract}?` +
                    `device_id=${searchParams.device_id}` +
                    `&client_id=${searchParams.client_id}` +
                    `&from_app=${searchParams.from_app}` +
                    `&app_ver=${searchParams.app_ver}` +
                    `&tz_name=${searchParams.tz_name}` +
                    `&tz_offset=${searchParams.tz_offset}` +
                    `&app_lang=${searchParams.app_lang}` +
                    `&fp_did=${searchParams.fp_did}` +
                    `&os=${searchParams.os}` + endParams;

                const headers = {
                    'User-Agent': navigator.userAgent,
                    'Referer': window.location.href,
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Encoding': 'gzip, deflate, br, zstd',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Cookie': document.cookie,
                    'Sec-Fetch-Dest': 'empty',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Site': 'same-origin'
                };

                GM_xmlhttpRequest({
                    method: 'GET',
                    url: apiUrl,
                    headers: headers,
                    onload: function(response) {
                        if (response.status === 200) {
                            try {
                                let data = JSON.parse(response.responseText);
                                let result = data.data.list;
                                result.forEach(item => {
                                    const address = item.address;
                                    const buy_volume = item.buy_volume_cur;
                                    const sell_volume = item.sell_volume_cur;
                                    const total_profit = item.profit;
                                    const buys = item.buy_tx_count_cur;
                                    const sells = item.sell_tx_count_cur;
                                    const rank = parseInt(item.wallet_tag_v2.replace('TOP', ''));
                                    const currentHold = Math.trunc(item.usd_value);
                                    const currentHoldPercent = (item.amount_percentage * 100).toFixed(2);
                                    const last_active_timestamp = item.last_active_timestamp;

                                    let holderRank = 0;
                                    let traderRank = 0;

                                    const currentData = traderDict.get(address);
                                    if (currentData) {
                                        holderRank = currentData.holderRank;
                                        traderRank = currentData.traderRank;
                                    }

                                    if (module === 'token_holders') {
                                        holderRank = rank;
                                    } else if (module === 'token_traders') {
                                        traderRank = rank;
                                    }

                                    traderDict.set(address, {
                                        holderRank: holderRank,
                                        traderRank: traderRank,
                                        buy_volume: buy_volume,
                                        sell_volume: sell_volume,
                                        profit: total_profit,
                                        buys: buys,
                                        sells: sells,
                                        currentHold: currentHold,
                                        currentHoldPercent: currentHoldPercent,
                                        last_active_timestamp: last_active_timestamp
                                    });
                                });

                                const currentToken = getCurrentToken();
                                console.log(`[fetchTopWalletStats(${module})] 获取代币 ${currentToken} 数据成功, 当前地址数量: ${traderDict.size}`);
                                return resolve(true);
                            } catch (e) {
                                return reject(e);
                            }
                        } else {
                            return reject(new Error(`请求失败: ${response.status}`));
                        }
                    },
                    onerror: function(error) {
                        return reject(error);
                    }
                });
            } catch (error) {
                return reject(error);
            }
        });
    }

    async function fetchWalletStats(address) {
        const searchParams = getSearchParams();
        if (!searchParams) {
            throw new Error(`[fetchWalletStats] 找不到searchParams`);
        }

        const network = window.location.pathname.split('/token/')[0].split('/')[1];
        const contract = window.location.pathname.split('/token/')[1];

        // 构造请求参数
        const apiUrl = `https://gmgn.ai/api/v1/wallet_token_info/${network}/${address}/${contract}?` +
            `device_id=${searchParams.device_id}` +
            `&client_id=${searchParams.client_id}` +
            `&from_app=${searchParams.from_app}` +
            `&app_ver=${searchParams.app_ver}` +
            `&tz_name=${searchParams.tz_name}` +
            `&tz_offset=${searchParams.tz_offset}` +
            `&app_lang=${searchParams.app_lang}` +
            `&fp_did=${searchParams.fp_did}` +
            `&os=${searchParams.os}` +
            `&limit=100` +
            `&maker=`;

        const headers = {
            'User-Agent': navigator.userAgent,
            'Referer': window.location.href,
            'Accept': 'application/json, text/plain, */*',
            'Cookie': document.cookie,
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        };

        // 通过队列发送请求
        return apiQueue.addRequest({
            method: "GET",
            url: apiUrl,
            headers: headers,
            onload: handleResponse,
            onerror: handleError
        });

        function handleResponse(response) {
            try {
                const data = JSON.parse(response.responseText);

                const dataResult = data.data;
                const address = dataResult.maker_info.address;
                const buy_volume = dataResult.history_bought_cost;
                const sell_volume = dataResult.history_sold_income;
                const total_profit = dataResult.total_profit;
                const buys = dataResult.buys;
                const sells = dataResult.sells;
                const currentHold = Math.trunc(dataResult.total_value);
                const currentbalance = parseFloat(dataResult.balance);

                // 如果没有数据 初始化 currentHoldPercent在这没有这个数据
                const currentData = traderDict.get(address);
                if (!currentData) {
                    const result = {
                        holderRank: 0,
                        traderRank: 0,
                        buy_volume: buy_volume,
                        sell_volume: sell_volume,
                        profit: total_profit,
                        buys: buys,
                        sells: sells,
                        currentHold: currentHold,
                        currentHoldPercent: null,
                        currentbalance: currentbalance,
                    };
                    traderDict.set(address, result);
                    return result;
                }

                // 更新 holderRank和traderRank 保持不变
                const result = {
                    holderRank: currentData.holderRank,
                    traderRank: currentData.traderRank,
                    buy_volume: buy_volume,
                    sell_volume: sell_volume,
                    profit: total_profit,
                    buys: buys,
                    sells: sells,
                    currentHold: currentHold,
                    currentHoldPercent: currentData.currentHoldPercent,
                    currentbalance: currentbalance,
                };

                traderDict.set(address, result);
                return result;
            } catch (e) {
                throw new Error(`数据解析失败: ${e.message}`);
            }
        }

        function handleError(error) {
            throw new Error(`请求失败: ${error.status || error.message}`);
        }
    }










    function getSearchParams() {
        if (g_searchParams) {
            return g_searchParams;
        }

        // 尝试从localStorage获取
        const stored = localStorage.getItem('gmgn_search_params');
        if (stored) {
            try {
                //console.log('[getSearchParams] 从存储中加载');
                return JSON.parse(stored);
            } catch (e) {
                console.log('[getSearchParams] 解析存储数据失败:', e);
                localStorage.removeItem('gmgn_search_params');
            }
        }

        return null;
    }

    function initXHR() {
        const originalOpen = XMLHttpRequest.prototype.open;
        const originalSend = XMLHttpRequest.prototype.send;

        XMLHttpRequest.prototype.open = function(method, url) {
            this._method = method;
            this._url = url;
            return originalOpen.apply(this, arguments);
        };

        XMLHttpRequest.prototype.send = function(body) {
            this.addEventListener('readystatechange', function() {
                if (this.readyState === 4 && this.status === 200) {
                    try {
                        const data = JSON.parse(this.responseText);
                        const url = this._url;
                        //console.log('[XHR] 请求地址:', this._url);
                        //console.log('[XHR] 响应数据:', data);

                        if (g_searchParams === null && url.includes('vas/api/v1/similar_coin')) {
                            // 提取查询参数部分
                            const queryString = url.split('?')[1]; // 获取 ? 后面的部分
                            g_searchParams = Object.fromEntries(new URLSearchParams(queryString));
                            localStorage.setItem('gmgn_search_params', JSON.stringify(g_searchParams));
                            console.log('[getResult] 设置新的 gmgn_search_params:', JSON.stringify(g_searchParams));
                        }
                    } catch (err) {
                        console.log('[XHR] 请求地址:', this._url);
                        console.log('[XHR] 响应文本:', this.responseText);
                    }
                }
            });
            return originalSend.apply(this, arguments);
        };
    }
})();