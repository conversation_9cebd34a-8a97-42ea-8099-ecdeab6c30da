// ==UserScript==
// @name         GMGN网络请求监控工具
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  监控页面网络请求的响应数据
// @match        https://gmgn.ai/*
// @grant        GM_xmlhttpRequest
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    let globalNetwork = null;
    let globalContract = null;
    let g_searchParams = null;

    let traderDict = new Map();
    
    let g_totalSupply = null;
    let g_marketCap = null;
    let g_tokenPrice = null;

    let firstRun = true;
    let isSettingFilter = false;

    initXHR();

    addEventListener('DOMContentLoaded', () => {
        waitForObserverElement();
        getTopWalletStats('token_traders');
        getTopWalletStats('token_holders');
    });
    // 请求队列类
    class RequestQueue {
        constructor(maxSize = 20, interval = 1000) {
            this.queue = []; // 请求队列
            this.maxSize = maxSize; // 队列最大容量
            this.interval = interval; // 请求间隔
            this.timerId = null; // 定时器ID
            this.activeRequest = null; // 当前活跃请求的xhr对象
        }
    
        // 添加请求到队列
        addRequest(requestConfig) {
            return new Promise((resolve, reject) => {
                // 队列满时移除最前面的请求
                if (this.queue.length >= this.maxSize) {
                    const removed = this.queue.shift();
                    removed.reject('[队列溢出] 请求被取消');
                }
    
                // 创建带取消功能的请求对象
                const request = {
                    ...requestConfig,
                    resolve,
                    reject,
                    xhr: null
                };
    
                this.queue.push(request);
    
                // 若未启动处理则立即启动
                if (!this.timerId) this.processQueue();
            });
        }
    
        // 处理队列
        processQueue() {
            if (this.queue.length === 0) {
                this.timerId = null;
                return;
            }
    
            const request = this.queue.shift();
            this.timerId = setTimeout(() => this.processQueue(), this.interval);
    
            try {
                this.activeRequest = GM_xmlhttpRequest({
                    ...request,
                    onload: (response) => {
                        this.activeRequest = null;
                        const processedResult = request.onload(response);
                        request.resolve(processedResult);
                    },
                    onerror: (error) => {
                        this.activeRequest = null;
                        const processedResult = request.onerror(error);
                        request.reject(processedResult);
                    },
                    onabort: () => {
                        this.activeRequest = null;
                        request.reject('[请求取消] 主动中止');
                    }
                });
    
                request.xhr = this.activeRequest;
            } catch (error) {
                request.reject(`[请求构造错误] ${error}`);
            }
        }
    
        // 取消所有请求
        cancelAll() {
            // 清除队列
            while (this.queue.length > 0) {
                const request = this.queue.shift();
                request.reject('[队列清除] 请求被取消');
            }
    
            // 中止当前请求
            if (this.activeRequest) {
                this.activeRequest.abort();
                this.activeRequest = null;
            }
    
            // 重置定时器
            clearTimeout(this.timerId);
            this.timerId = null;
        }
    }

    // 初始化全局请求队列
    const apiQueue = new RequestQueue(20, 100);

    globalNetwork = window.location.pathname.split('/token/')[0].split('/')[1];
    let pathnameAfter = window.location.pathname.split('/token/')[1];
    globalContract = pathnameAfter.includes('_') ? pathnameAfter.split('_')[1] : pathnameAfter;











    function waitForObserverElement() {
        waitForElement({
            target: '.chakra-tabs__tab-panels > div:nth-child(1)',
            callback: (element) => {
                console.log('找到目标元素:', element);
                initializeTradeHistory(element);
            },
            timeout: 5000,
            onTimeout: () => console.log('等待超时')
        });
    }

    function initializeTradeHistory(targetElement) {
        const isAll = () => document.querySelector('#activity-filter > div > div:nth-child(1)').classList.contains('text-text-100') == true;

        // 监控特定元素创建
        const globalTabObserver = new MutationObserver((mutations) => {
            if (!isTabActive()) {
                console.log('[initializeTradeHistory] 标签页不活动，不进行监控');
                return;
            }

            // 遍历所有发生的突变
            for (const mutation of mutations) {
                if (mutation.type === 'childList') {
                    for (const node of mutation.addedNodes) {
                        if (node.nodeType !== Node.ELEMENT_NODE || node.tagName !== 'DIV') {
                            continue;
                        }

                        // 如果主菜单是活动 子菜单是全部 刷新网站也是触发
                        if (node.classList.contains('g-table-body') && isAll()) {
                            if (isSettingFilter) {
                                console.log('[initializeTradeHistory] 正在设置过滤器，跳过处理');
                                continue;
                            }
                            console.log(`[initializeTradeHistory] 切换到活动 => 全部`);

                            isSettingFilter = true;
                            setTimeType();
                            setUsdFilter();
                            continue;
                        }

                        // 用户滚动创建新的交易
                        if (node.hasAttribute('data-index') && !isSettingFilter) {
                            //console.log('[initializeTradeHistory] 用户滚动:', node);
                            startCountdown();
                            onTradeCreated(node);
                        }
                    }
                } else if (mutation.type === 'attributes') {
                    //console.log('[initializeTradeHistory] 属性变化:', mutation.attributeName);
                    //console.log('[initializeTradeHistory] 目标元素:', mutation.target);

                    const element = mutation.target;

                    if (element.tagName !== 'A') {
                        continue;
                    }

                    const href = element.getAttribute('href');
                    if (!href || !href.includes('/tx/')) {
                        continue;
                    }

                    const closestDiv = element.closest('div[data-index]');
                    if (!closestDiv) {
                        continue;
                    }

                    onTradeCreated(closestDiv, true);
                }
            }
        });

        globalTabObserver.observe(targetElement, {
            attributes: true, // 观察属性变化
            childList: true, // 观察子节点的添加或移除
            subtree: true, // 观察所有后代节点（包括子节点和孙节点等）
            //characterData: true, // 观察文本内容变化
            //attributeOldValue: true, // 记录属性变化前的旧值
            //characterDataOldValue: true // 记录文本变化前的旧值
        });

        console.log('[initializeTradeHistory] globalTabObserver创建成功');
    }











    
    function onTokenSwitched(network, contract) {
        console.log('[onTokenSwitched] 切换到网络:', network, '合约:', contract);
        g_totalSupply = null;
        g_marketCap = null;
        g_tokenPrice = null;
    }

    function onTradeCreated(row, oldRow = false) {
        changeRowsStyle(row);

        if (oldRow) {
            removeRankSpan(row);
            if (!checkRowType(row)) {
                hideRow(row);
            } else {
                showRow(row);
                setTradeAndHighLight(row, 'yellow');
            }
            return;
        }

        if (!checkRowType(row)) {
            hideRow(row);
        }
    }





























    function setRowsAll() {
        const checkScroll = document.querySelector('.g-table-wrapper.scrollbar');
        if (!checkScroll) {
            console.log('[setRowsAll] 找不到checkScroll 重试');
            setTimeout(setRowsAll, 200);
            return;
        }

        const targetDiv = getTradrRowAll();
        if (targetDiv.length === 0) {
            //console.log('[setRowsAll] 找不到targetDiv 重试');
            setTimeout(setRowsAll, 200);
            return;
        }

        targetDiv.forEach(row => {
            setTradeAndHighLight(row, 'blue');
        });

        isSettingFilter = false;
    }

    function setTradeAndHighLight(row, color = '#FFFACD') {
        row.style.backgroundColor = color;
        setTimeout(() => {
            row.style.backgroundColor = '';
        }, 500);

        setTradeRank(row);
    }

    async function setTradeRank(row) {
        removeRankSpan(row);

        // 隐藏不是买入卖出的row
        if (!checkRowType(row)) {
            hideRow(row);
            return;
        }

        // 隐藏夹子和小额
        if (hideRowCheck(row)) {
            hideRow(row);
            return;
        }

        // 修改row的布局
        changeRowsStyle(row);   

        const address = row.querySelector('a a[href*="/address/"]').getAttribute('href').split('/address/')[1];
        let result = traderDict.get(address);

        const last_active_div = row.querySelector('.text-text-300 .css-k008qs');
        if (!last_active_div) {
            console.log('[setTradeRank] 找不到last_active_div');
            return;
        }

        const last_active_text = last_active_div.textContent;

        const last_active_timestamp = stringToTimestamp(last_active_text);
        const lastTx = row.querySelector('.css-fg98qe a');

        if (!result || last_active_timestamp  > result.last_active_timestamp) {
            try {
                result = await getWalletStats(address);
                //console.log('[setTradeRank]结果:', result);

                if (!result) {
                    console.log('[setTradeRank] 无法获得getWalletStats结果');
                    return;
                }
            } catch (error) {
                console.error('[setTradeRank] 获取地址:', address, '失败:', error);
                return;
            }
        }

        // 检查row是否被删除 await是异步
        if (!row || !row.isConnected) {
            console.log('[setTradeRank] row已被删除，跳过处理');
            return;
        }

        removeRankSpan(row);

        const infoSpan = createInfoSpan(row, result);
        if (!infoSpan) {
            console.log('[setTradeRank] 创建InfoSpan失败');
        }

        if (result.holderRank !== 0 || result.traderRank !== 0) {
            const rankSpan = createRankSpan(row, result);
            if (!rankSpan) {
                console.log('[setTradeRank] 创建RankSpan失败');
            }
        }

        const currentHoldSpan = createCurrentHoldSpan(row, result);
        if (!currentHoldSpan) {
            console.log('[setTradeRank] 创建CurrentHoldSpan失败');
        }
    }

    // 替换类型div
    function createRankSpan(row, result) {
        const rankSpan = document.createElement('div');
        rankSpan.style.backgroundColor = '#ffcccb';
        rankSpan.style.padding = '5px';
        rankSpan.style.borderRadius = '3px';
        //rankSpan.style.marginLeft = '10px';
        rankSpan.style.marginLeft = '0px';
        rankSpan.style.display = 'inline-block';
        //rankSpan.style.fontSize = '12px';
        rankSpan.style.fontSize = '14px';
        rankSpan.setAttribute('class', 'holder-rank');

        const holderRank = result.holderRank;
        const traderRank = result.traderRank;

        const spanHolder = holderRank ? `<span style="color:green">持有${holderRank}</span>` : '';
        const spanTrader = traderRank ? `<span style="color:green">交易${traderRank}</span>` : '';

        rankSpan.innerHTML = spanHolder + spanTrader;

        let position = row.querySelector('div > div > div:nth-child(2)');
        position.appendChild(rankSpan);

        return rankSpan;
    }

    // 替换数量div
    function createCurrentHoldSpan(row, result) {
        const currentHoldSpan = document.createElement('div');
        currentHoldSpan.style.backgroundColor = '#ffcccb';
        currentHoldSpan.style.padding = '5px';
        currentHoldSpan.style.borderRadius = '3px';
        //currentHoldSpan.style.marginLeft = '10px';
        currentHoldSpan.style.marginLeft = '0px';
        currentHoldSpan.style.display = 'inline-block';
        //currentHoldSpan.style.fontSize = '12px';
        currentHoldSpan.style.fontSize = '14px';
        currentHoldSpan.setAttribute('class', 'current-hold');

        const profit = parseFloat(result.profit).toFixed(0);

        const profitSpan = profit > 0 ? `<span style="color:green">+$${profit}</span>` : `<span style="color:red">$${profit}</span>`;

        let currentHoldPercent = null;
        if (result.currentHoldPercent) {
            currentHoldPercent = result.currentHoldPercent;
        } else {
            currentHoldPercent = (result.currentbalance / getTokenInfo().totalSupply).toFixed(2);
        }

        if (result.currentHold > 0) {
            currentHoldSpan.innerHTML = `<span style="color:green">持仓$${result.currentHold}(${currentHoldPercent}%)</span>`;
        } else {
            currentHoldSpan.innerHTML = `<span style="color:red">清仓</span> ${profitSpan}`;
        }

        let position = row.querySelector('div > div > div:nth-child(4)');
        position.appendChild(currentHoldSpan);

        return currentHoldSpan;;
    }
    
    // 替换交易者div
    function createInfoSpan(row, result) {
        let rank = result.traderRank;
        let profitInt = parseInt(result.profit);
        let buyVolumeInt = parseInt(result.buy_volume);
        let sellVolumeInt = parseInt(result.sell_volume);

        let infoSpan = document.createElement('div');
        infoSpan.style.backgroundColor = '#f0f8ff';
        infoSpan.style.padding = '5px';
        infoSpan.style.borderRadius = '3px';
        //infoSpan.style.marginLeft = '10px';
        infoSpan.style.marginLeft = '0px';
        infoSpan.style.display = 'inline-block';
        //infoSpan.style.fontSize = '12px';
        infoSpan.style.fontSize = '14px';

        infoSpan.setAttribute('class', 'ranker-info');

        //infoSpan.style.position = 'absolute';
        //infoSpan.style.right = '200px';
        //infoSpan.innerHTML = `<span style="color:#ff6600">交易者排名${rank}</span><span style="color:blue;">利润${profitInt}</span><span style="color:green;">买入${buyVolumeInt}</span><span style="color:red;">卖出${sellVolumeInt}</span>`;
        const spanProfit = `<span style="color:blue;">利润${profitInt}</span>`;
        const spanBuy = `<span style="color:green;">买入${buyVolumeInt}</span>`;
        const spanSell = `<span style="color:red;">卖出${sellVolumeInt}</span>`;

        infoSpan.innerHTML = spanProfit + spanBuy + spanSell;

        //style="position: relative;"
        //style="position: absolute;"
        // 交易者
        let position = row.querySelector('div > div > div:nth-child(6) a');
        //document.querySelector('.g-table-body [data-index] > div > div > div:nth-child(6) a');
        position.appendChild(infoSpan);

        return infoSpan;
    }

    function removeRankSpan(row) {
        const rankerInfo = row.querySelectorAll('.ranker-info');
        //console.log('[removeRankSpan] rankerInfo.length: ', rankerInfo.length);
        if (rankerInfo.length > 0) {
            rankerInfo.forEach(item => {
                item.remove();
            });
        }

        const holderRankSpan = row.querySelectorAll('.holder-rank');
        if (holderRankSpan.length > 0) {
            holderRankSpan.forEach(item => {
                item.remove();
            });
        }

        const currentHoldSpan = row.querySelectorAll('.current-hold');
        if (currentHoldSpan.length > 0) {
            currentHoldSpan.forEach(item => {
                item.remove();
            });
        }
    }
















    function changeRowsStyle(row, reset = false) {
        // 类型
        const typeDiv = row.querySelector(':scope > div > div > div:nth-child(2) > div');
        reset ? typeDiv.style.display = "block" : typeDiv.style.display = "none";
        // 数量
        const quantityDiv = row.querySelector(':scope > div > div > div:nth-child(4) > div');
        quantityDiv.style.display = "none";

        //const overflowDiv = row.querySelector('.css-1xz8urt');
        //overflowDiv.style.overflow = 'visible';
        //const overflowDiv2 = row.querySelector('.css-18n6119');
        //overflowDiv2.style.overflow = 'visible';
    }

    function checkRowType(row) {
        const type = row.querySelector(':scope > div > div > div:nth-child(2) > div').textContent.trim();
        if (type !== '买入' && type !== '卖出') {
            return false;
        }

        return true;
    }

    function hideRowCheck(row) {
        // 忽略夹子 金额小于20
        const buyUsdAmount = parseFloat(row.querySelector('div > div > div:nth-child(3) > div').title.replace('$', '').replace(',', '').trim());
        if (buyUsdAmount < 20) {
            //console.log('[setTradeRank] 金额小于20，跳过');
            return true;
        }
        
        const tradeCountElement = row.querySelector('.css-1y6lpld');
        if (!tradeCountElement) { // 如果找不到 不隐藏
            return false;
        }

        // 忽略夹子 交易次数大于150
        const tradeCountText = tradeCountElement.textContent.trim();
        if (parseInt(tradeCountText > 150) || tradeCountText.includes('K')) {
            //console.log('[setTradeRank] 交易次数大于150，跳过');
            return true;
        }

        return false;
    }

    function hideRow(row) {
        row.style.display = 'none';
        row.setAttribute('data-hidden', 'true');
    }

    function showRow(row) {
        row.style.display = '';
        row.setAttribute('data-hidden', 'false');
    }












    function setUsdFilter() {
        const filterButton = document.querySelector('.css-1mjhmtt button');
        if (!filterButton) {
            console.log('[setUsdFilter] 找不到filterButton');
            return;
        }

        if (filterButton.querySelector('svg').classList.contains('text-text-100')) {
            //console.log('[setUsdFilter] filterButton已经启用');
            //return;
        }

        filterButton.click();

        let inputMin = null;
        let inputMax = null;

        const inputs = document.querySelectorAll('.css-y7wxkx input');
        if (inputs.length != 2) {
            console.log('[setUsdFilter] 找不到inputs');
            return;
        }

        inputMin = inputs[0];
        inputMax = inputs[1];
        
        //const minUsd = 20 / 1000;
        const marketCap = getTokenInfo().marketCap;
        
        let minUsd = marketCap / 2000;
        if (minUsd >  5000) {
            minUsd = 5000;
        }

        console.log('[setUsdFilter] g_marketCap:', marketCap , 'minUsd:', minUsd);
        let maxUsd = 0;

        setTimeout(() => {
            Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value').set.call(inputMin, minUsd / 1000);
            inputMin.dispatchEvent(new Event('input', { bubbles: true }));

            if (maxUsd) {
                Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value').set.call(inputMax, maxUsd / 1000);
                inputMax.dispatchEvent(new Event('input', { bubbles: true }));
            }

            // 应用
            document.querySelector('.css-1elebcq div:nth-child(2)').click();
            setTimeout(() => {
                setRowsAll();
            }, 1000);
        }, 1);
    }

    function setTimeType() {
        let last_active_div = document.querySelector('.g-table-body [data-index] .text-text-300 .css-k008qs');
        if (!last_active_div) {
            console.log('[setTradeRank] 找不到last_active_div 尝试切换');
            document.querySelector('.css-vgxhkg').click();
            setTimeout(() => {
                last_active_div = document.querySelector('.g-table-body [data-index] .text-text-300 .css-k008qs');
                if (!last_active_div) {
                    console.log('[setTradeRank] 切换后仍找不到last_active_div');
                } else {
                    console.log('[setTradeRank] 切换后找到last_active_div');
                }
            });
        }
    }

    function getTokenInfo() {
        if (!g_totalSupply) {
            const addressElement = document.querySelectorAll('a.css-1dgetxa');
            if (addressElement.length != 2) {
                console.log('[initTokenInfo] addressElement.length != 2');
            }
            const address = addressElement[1].href.split('?q=')[1];
            
            const priceElement = document.querySelector('div.text-\\[18px\\].leading-\\[20px\\].font-semibold');
            if (!priceElement) {
                console.log('[initTokenInfo] 找不到priceElement 重试');
                return;
            }

            g_tokenPrice = parseSpecialNumber(priceElement.textContent);

            const totalSupplyElement = document.querySelector('.p-12px.gap-10px.border-b-line-100 > div:nth-child(5) > div:nth-child(2)');
            if (!totalSupplyElement) {
                console.log('[initTokenInfo] 找不到totalSupplyElement');
                return;
            }

            const totalSupplyText = totalSupplyElement.textContent;
            if (totalSupplyText.includes('B')) {
                g_totalSupply = parseFloat(totalSupplyText.replace('B', '')) * 1000000000;
                //console.log('[initTokenInfo] 转换为数字 totalSupply:', g_totalSupply);
            } else if (totalSupplyText.includes('M')) {
                g_totalSupply = parseFloat(totalSupplyText.replace('M', '')) * 1000000;
                //console.log('[initTokenInfo] 转换为数字 totalSupply:', g_totalSupply);
            }

            g_marketCap = g_totalSupply * g_tokenPrice;

            console.log('[initTokenInfo] 初始化 address:', address, ' totalSupply:', g_totalSupply, ' marketCap:', g_marketCap, ' tokenPrice:', g_tokenPrice);
        }

        return {
            totalSupply: g_totalSupply,
            marketCap: g_marketCap,
            tokenPrice: g_tokenPrice
        };
    }










































    function initXHR() {
        const originalOpen = XMLHttpRequest.prototype.open;
        const originalSend = XMLHttpRequest.prototype.send;

        XMLHttpRequest.prototype.open = function(method, url) {
            this._method = method;
            this._url = url;
            return originalOpen.apply(this, arguments);
        };

        XMLHttpRequest.prototype.send = function(body) {
            this.addEventListener('readystatechange', function() {
                if (this.readyState === 4 && this.status === 200) {
                    try {
                        const data = JSON.parse(this.responseText);
                        //console.log('[XHR] 请求地址:', this._url);
                        //console.log('[XHR] 响应数据:', data);

                        // 在这里可以对响应数据进行处理
                        onGetResult(this._url, data);
                    } catch (err) {
                        //console.log('[XHR] 请求地址:', this._url);
                        //console.log('[XHR] 响应文本:', this.responseText);
                    }
                }
            });
            return originalSend.apply(this, arguments);
        };
    }

    function onGetResult(url, jsonData) {
        if (g_searchParams === null && url.includes('vas/api/v1/similar_coin')) {
            // 提取查询参数部分
            const queryString = url.split('?')[1]; // 获取 ? 后面的部分
            g_searchParams = Object.fromEntries(new URLSearchParams(queryString));
            localStorage.setItem('gmgn_search_params', JSON.stringify(g_searchParams));
            console.log('[getResult] 设置新的 gmgn_search_params:', JSON.stringify(g_searchParams));
        }

        if (url.includes('/api/v1/mutil_window_token_link_rug_vote')) {
            const baseUrl = url.split('?')[0].split('rug_vote/')[1];
            const network = baseUrl.split('/')[0];
            const contract = baseUrl.split('/')[1];
            if (network === globalNetwork && contract === globalContract) {
                return;
            }

            globalNetwork = network;
            globalContract = contract;
            onTokenSwitched(network, contract);

            return;
        }

        // 获取代币价格
        if (url.includes('/api/v1/mutil_window_token_info')) {
            return;
            g_tokenPrice = jsonData.data[0].price.price;
            return;
        }

        // 获取代币价格
        if (url.includes('/walletstat/')) {
            return;
            g_tokenPrice = jsonData.data.price;
            g_marketCap = jsonData.data.market_cap;
            g_totalSupply = jsonData.data.total_supply;
            console.log('[walletstat] 当前时间: ', new Date().toISOString());
            console.log('[walletstat] 代币价格:', Price, '市值:', market_cap, '总供应量:', total_supply);
            return;
        }

        //https://gmgn.ai/defi/quotation/v1/smartmoney/sol/walletstat/
    }

    async function getWalletStats(address) {
        const searchParams = getSearchParams();
        // 前置校验
        if (!searchParams) {
            console.log('[getWalletStats] 找不到searchParams');
            return null;
        }

        if (!globalNetwork || !globalContract) {
            console.log('[getWalletStats] 找不到globalNetwork或globalContract');
            return null;
        }

        // 构造请求参数
        const apiUrl = `https://gmgn.ai/api/v1/wallet_token_info/${globalNetwork}/${address}/${globalContract}?` +
            `device_id=${searchParams.device_id}` +
            `&client_id=${searchParams.client_id}` +
            `&from_app=${searchParams.from_app}` +
            `&app_ver=${searchParams.app_ver}` +
            `&tz_name=${searchParams.tz_name}` +
            `&tz_offset=${searchParams.tz_offset}` +
            `&app_lang=${searchParams.app_lang}` +
            `&fp_did=${searchParams.fp_did}` +
            `&os=${searchParams.os}` +
            `&limit=100` +
            `&maker=`;

        const headers = {
            'User-Agent': navigator.userAgent,
            'Referer': window.location.href,
            'Accept': 'application/json, text/plain, */*',
            'Cookie': document.cookie,
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        };

        // 通过队列发送请求
        return apiQueue.addRequest({
            method: "GET",
            url: apiUrl,
            headers: headers,
            onload: handleResponse,
            onerror: handleError
        });

        function handleResponse(response) {
            try {
                const data = JSON.parse(response.responseText);

                const dataResult = data.data;
                const address = dataResult.maker_info.address;
                const buy_volume = dataResult.history_bought_cost;
                const sell_volume = dataResult.history_sold_income;
                const total_profit = dataResult.total_profit;
                const buys = dataResult.buys;
                const sells = dataResult.sells;
                const currentHold = Math.trunc(dataResult.total_value);
                const currentbalance = parseFloat(dataResult.balance);

                // 如果没有数据 初始化 currentHoldPercent在这没有这个数据
                const currentData = traderDict.get(address);
                if (!currentData) {
                    const result = {
                        holderRank: 0,
                        traderRank: 0,
                        buy_volume: buy_volume,
                        sell_volume: sell_volume,
                        profit: total_profit,
                        buys: buys,
                        sells: sells,
                        currentHold: currentHold,
                        currentHoldPercent: null,
                        currentbalance: currentbalance,
                    };
                    traderDict.set(address, result);
                    return result;
                }

                // 更新 holderRank和traderRank 保持不变
                const result = {
                    holderRank: currentData.holderRank,
                    traderRank: currentData.traderRank,
                    buy_volume: buy_volume,
                    sell_volume: sell_volume,
                    profit: total_profit,
                    buys: buys,
                    sells: sells,
                    currentHold: currentHold,
                    currentHoldPercent: currentData.currentHoldPercent,
                    currentbalance: currentbalance,
                };

                traderDict.set(address, result);
                return result;
            } catch (e) {
                throw new Error(`数据解析失败: ${e.message}`);
            }
        }

        function handleError(error) {
            //throw new Error(`请求失败: ${error.status || error.message}`);
            return `请求失败: ${error.status || error.message}`;
        }
    }

    function getTopWalletStats(module) {
        if (module === '') {
            console.log(`[getTopWalletStats(${module})] 找不到module`);
            return;
        }

        const searchParams = getSearchParams();
        if (!searchParams) {
            console.log(`[getTopWalletStats(${module})] 找不到searchParams`);
            return;
        }

        if (!globalNetwork || !globalContract) {
            console.log(`[getTopWalletStats(${module})] 找不到globalNetwork或者globalContract`);
            return;
        }

        let endParams = null;
        if (module === 'token_holders') {
            endParams = '&limit=100&cost=20&orderby=amount_percentage&direction=desc';
        } else if (module === 'token_traders') {
            endParams = '&limit=100&orderby=realized_profit&direction=desc';
        }

        // API 请求配置
        const apiUrl = `https://gmgn.ai/vas/api/v1/${module}/${globalNetwork}/${globalContract}?` +
            `device_id=${searchParams.device_id}` +
            `&client_id=${searchParams.client_id}` +
            `&from_app=${searchParams.from_app}` +
            `&app_ver=${searchParams.app_ver}` +
            `&tz_name=${searchParams.tz_name}` +
            `&tz_offset=${searchParams.tz_offset}` +
            `&app_lang=${searchParams.app_lang}` +
            `&fp_did=${searchParams.fp_did}` +
            `&os=${searchParams.os}` + endParams;

        // 配置请求头，模拟网站行为
        const headers = {
            'User-Agent': navigator.userAgent,
            'Referer': window.location.href,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cookie': document.cookie,
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        };

        //console.log(`[getTopWalletStats(${module})] 准备请求Url: ${apiUrl}`);

        // 发送 API 请求
        GM_xmlhttpRequest({
            method: 'GET',
            url: apiUrl,
            headers: headers,
            onload: function(response) {
                if (response.status === 200) {
                    try {
                        let data = JSON.parse(response.responseText);
                        let result = data.data.list;
                        result.forEach(item => {
                            const address = item.address;
                            const buy_volume = item.buy_volume_cur;
                            const sell_volume = item.sell_volume_cur;
                            const total_profit = item.profit;
                            const buys = item.buy_tx_count_cur;
                            const sells = item.sell_tx_count_cur;
                            const rank = parseInt(item.wallet_tag_v2.replace('TOP', ''));
                            const currentHold = Math.trunc(item.usd_value);
                            const currentHoldPercent = (item.amount_percentage * 100).toFixed(2);
                            const last_active_timestamp = item.last_active_timestamp;

                            const DEV = false;
                            if (DEV) {
                                console.log('地址:', address);
                                console.log('买入金额:', buy_volume);
                                console.log('卖出金额:', sell_volume);
                                console.log('总利润:', total_profit);
                                console.log('买入次数:', buys);
                                console.log('卖出次数:', sells);
                                console.log('当前持有:', currentHold);
                                console.log('当前持有百分比:', currentHoldPercent);
                                console.log('最后活跃时间:', last_active_timestamp);//int
                            }

                            let holderRank = 0;
                            let traderRank = 0;

                            const currentData = traderDict.get(address);
                            if (currentData) {
                                holderRank = currentData.holderRank;
                                traderRank = currentData.traderRank;
                            }

                            if (module === 'token_holders') {
                                holderRank = rank;
                            } else if (module === 'token_traders') {
                                traderRank = rank;
                            }

                            traderDict.set(address, {
                                holderRank: holderRank,
                                traderRank: traderRank,
                                buy_volume: buy_volume,
                                sell_volume: sell_volume,
                                profit: total_profit,
                                buys: buys,
                                sells: sells,
                                currentHold: currentHold,
                                currentHoldPercent: currentHoldPercent,
                                last_active_timestamp: last_active_timestamp
                            });
                        });
                    } catch (e) {
                        console.error('解析 JSON 失败:', e);
                        console.log('数据解析失败，请检查控制台');
                    }
                } else {
                    console.error('请求失败:', response.status, response.responseText);
                    console.log('请求失败: ' + response.status);
                }
            },
            onerror: function(error) {
                console.error('请求出错:', error);
                console.log('请求出错，请检查控制台');
            }
        });
    }














































    const DB_BASE_URL = 'http://localhost:4000';
    // --- 新增: 保存统计数据到后端的函数 ---
    async function db_saveTraderStats(statsData) {
        if (!globalNetwork ||!globalContract) {
            console.log('[db_saveTraderStats] 找不到globalNetwork或globalContract');
            return null;
        }

        const payload = {
            address: statsData.address,
            token_address: globalContract,
            chain: globalNetwork,
            holderRank: statsData.holderRank,
            traderRank: statsData.traderRank,
            buy_volume: statsData.buy_volume,
            sell_volume: statsData.sell_volume,
            profit: statsData.profit,
            buys: statsData.buys,
            sells: statsData.sells,
            currentHold: statsData.currentHold,
            currentHoldPercent: statsData.currentHoldPercent
        };

        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: `${DB_BASE_URL}/tradestats`,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                data: JSON.stringify(payload),
                onload: function(response) {
                    if (response.status === 200 || response.status === 201) { // 201 Created 也可能是成功的状态码
                        try {
                            const result = JSON.parse(response.responseText);
                            if (result.success) {
                                console.log('[saveTraderStats] 成功保存数据到后端:', result.message);
                                resolve(result.data);
                            } else {
                                console.error('[saveTraderStats] 后端保存数据失败:', result.error || result.message);
                                reject(new Error(result.error || result.message || '后端保存失败'));
                            }
                        } catch (e) {
                            console.error('[saveTraderStats] 解析后端保存响应失败:', e);
                            reject(new Error('解析后端保存响应失败'));
                        }
                    } else {
                        console.error('[saveTraderStats] 保存数据到后端请求失败:', response.status, response.statusText, response.responseText);
                        reject(new Error(`后端保存请求失败: ${response.status}`));
                    }
                },
                onerror: function(error) {
                    console.error('[Backend] 保存数据到后端网络错误:', error);
                    reject(new Error('后端保存网络错误'));
                }
            });
        });
    }

    function safeReorderForVirtualScroll() {
        const divs = Array.from(document.querySelectorAll('.g-table-body div[data-index]'));
        
        // 1. 保存所有 div 的原始 top
        const originalStyles = divs.map(div => ({
            top: div.style.top,
        }));
        
        // 2. 分离需要移动的 div（根据data-hidden属性）
        const normalDivs = divs.filter(div => div.getAttribute('data-hidden') !== 'true');
        const bottomDivs = divs.filter(div => div.getAttribute('data-hidden') === 'true');
        
        // 3. 合并数组：正常 div 在前，隐藏 div 在后
        const reorderedDivs = [...normalDivs, ...bottomDivs];
        
        // 4. 重新计算 top
        reorderedDivs.forEach((div, index) => {
            if (div.style.top !== originalStyles[index].top) {
                div.style.top = originalStyles[index].top;
                console.log('[VirtualScroll] 重新设置top:', div.style.top);
            }
        });
    }
















































    // 倒计时
    let floatWindow = null;
    let mutationTimeout = null;
    let countdownInterval;
    let countdownSeconds = 1;

    function startCountdown() {
        function createFloatWindow(text = '') {
            const div = document.createElement('div');
            div.id = 'scroll-countdown-window';
            div.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                width: 150px;
                padding: 10px;
                background-color: #333;
                color: #fff;
                border-radius: 5px;
                box-shadow: 0 0 10px rgba(0,0,0,0.5);
                z-index: 9999;
                text-align: center;
                font-family: Arial, sans-serif;
            `;
            div.textContent = text;
            document.body.appendChild(div);
            return div;
        }

        if (!floatWindow) {
            floatWindow = createFloatWindow('等待滚动...');
        }

        clearTimeout(mutationTimeout);
        clearInterval(countdownInterval);

        countdownSeconds = 1;
        floatWindow.textContent = `倒计时: ${countdownSeconds}s`;

        // 倒计时显示
        countdownInterval = setInterval(() => {
            countdownSeconds--;
            floatWindow.textContent = `倒计时: ${countdownSeconds}s`;
            if (countdownSeconds <= 0) {
                clearInterval(countdownInterval);
            }
        }, 1000);

        mutationTimeout = setTimeout(() => {
            console.log('用户滚动结束，检查交易');
            setRowsAll();
            floatWindow.textContent = '检查完成';
        }, 1000);
    }

    function getSearchParams() {
        if (g_searchParams) {
            return g_searchParams;
        }

        // 尝试从localStorage获取
        const stored = localStorage.getItem('gmgn_search_params');
        if (stored) {
            try {
                console.log('[getSearchParams] 从存储中加载');
                return JSON.parse(stored);
            } catch (e) {
                console.log('[getSearchParams] 解析存储数据失败:', e);
                localStorage.removeItem('gmgn_search_params');
            }
        }

        return null;
    }

    function getTradrRowAll() {
        const rows = document.querySelectorAll('.g-table-body [data-index]');
        return rows.length > 0 ? rows : 0;
    }



















    // 检查当前标签页是否活动的函数
    function isTabActive() {
        return !document.hidden;
    }

    // 解析特殊格式的数字
    function parseSpecialNumber(text) {
        // 移除所有空格 $和,符号 如:$0.0₄989,13 -> 0.0₄98913
        text = text.trim().replace(/[$,]/g, '');
        
        // 处理特殊下标数字(表示10的幂)
        const subscriptMap = {
            '₀': 0, '₁': 1, '₂': 2, '₃': 3, '₄': 4,
            '₅': 5, '₆': 6, '₇': 7, '₈': 8, '₉': 9,
            '₁₀': 10, '₁₁': 11, '₁₂': 12, '₁₃': 13, '₁₄': 14,
            '₁₅': 15
        };
        
        // 找到所有下标数字
        let power = 0;
        for (const [subscript, value] of Object.entries(subscriptMap)) {
            if (text.startsWith('0.0' + subscript)) { // 0.0₄98913
                // 移除下标并记录数字
                text = text.replace('0' + subscript, ''); // 0.0₄98913 -> 0.98913
                power = -value;  // 需要是负数幂，因为是小数 // power = -4
            }
        }
        
        // 解析基础数字
        const baseNumber = parseFloat(text); // 0.98913

        // 计算最终结果 (基础数字 * 10^幂)
        return baseNumber * Math.pow(10, power); // 0.98913 -> 0.000098913
    }

    function stringToTimestamp(str) {
        return Math.floor(new Date(`${new Date().getFullYear()}/${str}`.replace(/-/g, '/')).getTime() / 1000);   
    }

    const getCostTime = (() => {
        const initTime = Date.now(); // 只初始化一次
        return () => Date.now() - initTime;
    })();

    function timerWait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 等待DOM元素出现并执行回调
     * @param {Object} options 配置项
     * @param {string|function} options.target 目标选择器或判断函数
     * @param {function} options.callback 目标出现时的回调函数
     * @param {boolean} options.once 是否只执行一次 默认true
     * @param {number} options.timeout 超时时间(ms) 默认0表示永不超时
     * @param {function} options.onTimeout 超时回调
     * @param {Element} options.root 观察根元素 默认document.documentElement
     * @returns {Object} observer实例，包含disconnect方法
     */
    function waitForElement({
        target,
        callback,
        once = true,
        timeout = 0,
        onTimeout,
        root = document.documentElement
    }) {
        // 检查直接存在的元素
        if (typeof target === 'string') {
            const element = document.querySelector(target);
            if (element) {
                callback(element);
                if (once) return { disconnect: () => {} };
            }
        }

        let timeoutId;
        
        // 如果不存在，则观察DOM变化
        const observer = new MutationObserver((mutations) => {
            for (const mutation of mutations) {
                if (mutation.type === 'childList') {
                    for (const node of mutation.addedNodes) {
                        if (node.nodeType !== Node.ELEMENT_NODE) continue;
                        
                        // 检查新增节点
                        let matchedElement = null;
                        
                        if (typeof target === 'string') {
                            // 选择器匹配
                            if (node.matches(target)) {
                                matchedElement = node;
                            } else {
                                matchedElement = node.querySelector(target);
                            }
                        } else if (typeof target === 'function') {
                            // 自定义判断函数
                            if (target(node)) {
                                matchedElement = node;
                            } else {
                                const children = node.querySelectorAll('*');
                                for (const child of children) {
                                    if (target(child)) {
                                        matchedElement = child;
                                        break;
                                    }
                                }
                            }
                        }

                        if (matchedElement) {
                            callback(matchedElement);
                            if (once) {
                                observer.disconnect();
                                if (timeoutId) clearTimeout(timeoutId);
                                return;
                            }
                        }
                    }
                }
            }
        });

        // 开始观察
        observer.observe(root, {
            childList: true,
            subtree: true
        });

        // 设置超时
        if (timeout > 0) {
            timeoutId = setTimeout(() => {
                observer.disconnect();
                if (onTimeout) onTimeout();
            }, timeout);
        }

        return observer;
    }
})();

